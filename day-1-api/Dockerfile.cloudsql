# syntax = docker/dockerfile:1

# Adjust NODE_VERSION as desired
ARG NODE_VERSION=18.20.7
FROM node:${NODE_VERSION}-slim as base

LABEL fly_launch_runtime="Node.js/Prisma"

# Node.js/Prisma app lives here
WORKDIR /app

# Set production environment
ENV NODE_ENV="production"

# Throw-away build stage to reduce size of final image
FROM base as build

# Install packages needed to build node modules
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y build-essential node-gyp openssl pkg-config python-is-python3 wget ca-certificates && \
    rm -rf /var/lib/apt/lists /var/cache/apt/archives

# Install Cloud SQL Proxy
RUN wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O cloud_sql_proxy && \
    chmod +x cloud_sql_proxy && \
    mv cloud_sql_proxy /usr/local/bin/

# Install node modules
COPY package-lock.json package.json ./
RUN npm ci --include=dev

# Generate Prisma Client
COPY prisma .
RUN npx prisma generate

# Copy application code
COPY . .

# Build application
RUN npm run build

# Remove development dependencies
RUN npm prune --omit=dev

# Final stage for app image
FROM base

# Install packages needed for deployment and Cloud SQL Proxy
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y openssl wget ca-certificates && \
    wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O cloud_sql_proxy && \
    chmod +x cloud_sql_proxy && \
    mv cloud_sql_proxy /usr/local/bin/ && \
    rm -rf /var/lib/apt/lists /var/cache/apt/archives

# Copy built application
COPY --from=build /app /app

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Start Cloud SQL Proxy in background if CLOUD_SQL_CONNECTION_NAME is set\n\
if [ ! -z "$CLOUD_SQL_CONNECTION_NAME" ]; then\n\
  echo "Starting Cloud SQL Proxy for $CLOUD_SQL_CONNECTION_NAME"\n\
  cloud_sql_proxy -instances=$CLOUD_SQL_CONNECTION_NAME=tcp:5432 &\n\
  # Wait a moment for proxy to start\n\
  sleep 3\n\
fi\n\
\n\
# Start the application\n\
echo "Starting Node.js application"\n\
exec npm run start' > /app/start.sh && chmod +x /app/start.sh

# Start the server by default, this can be overwritten at runtime
EXPOSE 3000
CMD ["/app/start.sh"]