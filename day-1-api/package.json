{"name": "day-one", "version": "1.0.0", "description": "", "main": "build/src/server.js", "scripts": {"dev": "concurrently \"nodemon\" \"nodemon -x tsoa spec-and-routes\"", "build": "tsoa spec-and-routes && tsc --outDir build --experimentalDecorators", "start": "node build/src/server.js", "lint": "eslint \"src/**/*.{js,ts}\"", "lint:fix": "eslint \"src/**/*.{js,ts}\" --fix", "format": "prettier \"src/**/*.{js,ts,json}\" --write && npx prisma format", "prepare": "husky", "tsoa": "tsoa", "migrate:dev": "npx prisma migrate dev --name", "migrate": "npx prisma migrate deploy", "generate": "npx prisma generate"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.0.1", "@tsoa/runtime": "^6.4.0", "bcryptjs": "^2.4.3", "cls-rtracer": "^2.6.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-rate-limit": "^7.4.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "resend": "^4.0.0", "rotating-file-stream": "^3.2.5", "stripe": "^17.2.0", "swagger-ui-express": "^5.0.1", "tsoa": "6.4", "tsyringe": "^4.8.0", "twilio": "^5.3.2", "winston": "^3.14.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.11.0", "@flydotio/dockerfile": "^0.5.9", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.7", "@types/morgan": "^1.9.9", "@types/node": "^22.5.5", "@types/numeral": "^2.0.5", "@types/swagger-ui-express": "^4.1.6", "concurrently": "^9.0.1", "eslint": "^9.11.0", "globals": "^15.9.0", "husky": "^9.1.6", "nodemon": "^3.1.7", "prettier": "^3.3.3", "prisma": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.6.2", "typescript-eslint": "^8.6.0"}}