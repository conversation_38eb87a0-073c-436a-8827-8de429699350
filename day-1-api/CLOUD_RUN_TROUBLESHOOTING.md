# Cloud Run Database Connection Troubleshooting

## Problem
PrismaClientInitializationError (P1012) - Cannot connect to database from Cloud Run

## Root Cause
Cloud Run cannot connect to Cloud SQL using direct IP address (************). Cloud Run requires special configuration to connect to Cloud SQL instances.

## Solutions

### Solution 1: Use Cloud SQL Connection (Recommended)

1. **Update your deployment command:**
```bash
# Use the deploy-cloud-run.sh script provided
chmod +x deploy-cloud-run.sh
./deploy-cloud-run.sh
```

2. **Key changes in the deployment:**
   - Added `--add-cloudsql-instances` flag
   - Updated DATABASE_URL to use Unix socket path
   - Set all environment variables directly in deployment

### Solution 2: Use Cloud SQL Proxy

1. **Use the alternative deployment script:**
```bash
chmod +x deploy-with-proxy.sh
./deploy-with-proxy.sh
```

### Solution 3: Manual Cloud Console Configuration

1. **In Google Cloud Console:**
   - Go to Cloud Run → day-one-api service
   - Click "Edit & Deploy New Revision"
   - Go to "Connections" tab
   - Add Cloud SQL connection: `day-one-465214:us-central1:day-one-db`

2. **Update Environment Variables:**
   ```
   DATABASE_URL=postgresql://day_one_user:day1api@localhost:5432/day_one_db?host=/cloudsql/day-one-465214:us-central1:day-one-db
   DIRECT_URL=postgresql://day_one_user:day1api@localhost:5432/day_one_db?host=/cloudsql/day-one-465214:us-central1:day-one-db
   ```

## Verification Steps

1. **Check Cloud SQL instance details:**
   - Instance ID: `day-one-db`
   - Region: `us-central1`
   - Database: `day_one_db`
   - User: `day_one_user`

2. **Test the connection:**
   ```bash
   # After deployment, check logs
   gcloud run logs read day-one-api --region=us-central1
   ```

3. **Verify service is running:**
   ```bash
   curl https://day-one-api-[hash]-uc.a.run.app/
   ```

## Common Issues

1. **Wrong connection name format:**
   - Correct: `PROJECT_ID:REGION:INSTANCE_NAME`
   - Example: `day-one-465214:us-central1:day-one-db`

2. **Database user permissions:**
   - Ensure `day_one_user` has proper permissions
   - Check if user can connect from Cloud SQL

3. **Firewall/Network issues:**
   - Cloud SQL should allow connections from Cloud Run
   - Check Cloud SQL authorized networks

## Quick Fix Command

Run this single command to redeploy with proper Cloud SQL connection:

```bash
gcloud run deploy day-one-api \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 3000 \
  --add-cloudsql-instances day-one-465214:us-central1:day-one-db \
  --set-env-vars DATABASE_URL="postgresql://day_one_user:day1api@localhost:5432/day_one_db?host=/cloudsql/day-one-465214:us-central1:day-one-db" \
  --set-env-vars DIRECT_URL="postgresql://day_one_user:day1api@localhost:5432/day_one_db?host=/cloudsql/day-one-465214:us-central1:day-one-db"
```

**Note:** Replace `day-one-465214` with your actual project ID if different.