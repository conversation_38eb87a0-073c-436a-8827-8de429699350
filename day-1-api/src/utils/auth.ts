import * as jwt from 'jsonwebtoken'
import { EnvVars } from '../constants/env'
import { JwtPayload, JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken'
import { User, UserPayload } from '../types/req.types'

export type TokenDetails = {
  token: string
  iat: number
  expiresIn: number
}

export const UserPayloadFromUser = (user: User): UserPayload => {
  const { email, phoneNumber, id, createdAt, updatedAt } = user

  return {
    email,
    phoneNumber,
    id,
    createdAt,
    updatedAt,
  }
}

export class JwtService {
  static generateToken(
    payload: UserPayload,
    type: 'access' | 'refresh' = 'access',
  ): TokenDetails {
    const secret = type === 'access' ? EnvVars.jwtSecret : EnvVars.refreshSecret
    const expiry =
      type === 'access' ? EnvVars.jwtExpiry : EnvVars.refreshTokenExpiry
    const token = jwt.sign(payload, secret, {
      expiresIn: expiry,
    })
    return {
      token,
      iat: (jwt.decode(token) as JwtPayload)?.iat ?? Date.now(),
      expiresIn: (jwt.decode(token) as JwtPayload)?.exp ?? Date.now(),
    }
  }

  static verifyToken(
    token: string,
    type: 'access' | 'refresh' = 'access',
  ): {
    verified: boolean
    user: UserPayload | null
    error: null | string
  } {
    const secret = type === 'access' ? EnvVars.jwtSecret : EnvVars.refreshSecret

    try {
      const user = jwt.verify(token, secret) as JwtPayload
      delete user.exp
      delete user.iat

      return {
        verified: true,
        user: user as UserPayload,
        error: null,
      }
    } catch (error) {
      if (error instanceof JsonWebTokenError) {
        return {
          verified: false,
          user: null,
          error: error.message,
        }
      }
      if (error instanceof TokenExpiredError) {
        return {
          verified: false,
          user: null,
          error: error.message,
        }
      }
      return {
        verified: false,
        user: null,
        error: 'Unauthenticated',
      }
    }
  }
}

export const userHasCompletedRegistrationProcess = (user: User) => {
  return (
    user.biometricsSetAt &&
    user.emailVerifiedAt &&
    user.passwordSetAt &&
    user.phoneVerifiedAt &&
    user.pinSetAt
  )
}

export const isAuthEndpoint = (endpoint: string) => {
  return endpoint.includes('auth')
}
export const isOnboardingEndpoint = (endpoint: string) => {
  return [
    '/register',
    '/resend-email-otp',
    '/verify-email',
    '/add-password',
    '/add-phone',
    '/resend-otp',
    '/verify-phone',
    '/enable-biometrics',
    '/add-pin',
  ].includes(endpoint)
}
