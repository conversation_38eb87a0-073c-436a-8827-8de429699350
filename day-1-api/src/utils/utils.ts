// import { EnvVars } from '../constants.ts/env'

export const safeString = (val: unknown, fallback: string = ''): string => {
  return typeof val === 'string' ? val : fallback
}

export const serialize = (num: number): string => {
  if (num === 0) return '0'

  if (Math.abs(num) < 10) {
    if (num < 0) {
      return '-0' + Math.abs(num)
    } else {
      return '0' + num
    }
  } else {
    return String(num)
  }
}

export const filterByMatchers =
  <T>(arrayOfMatchers: ((a: T) => string)[]) =>
  (arr: T[], query: string) => {
    const matchedItems = arrayOfMatchers.map((matcher) => {
      return arr.filter((order) =>
        (matcher(order) || '').toLowerCase().includes(query.toLowerCase()),
      )
    })

    if (!matchedItems.length) return []

    return matchedItems.flatMap((e) => e)
  }

export const EP = {
  Array: Object.freeze([]) as [],
  Object: Object.freeze({}),
}

export function safeArray<T>(value: unknown): T[] {
  if (Array.isArray(value)) {
    return value as T[]
  } else {
    return []
  }
}

export const isNumber = (n: unknown): boolean => {
  return !Object.is(NaN, Number(n))
}

/**
 * Why? yieldToMain
 * https://web.dev/articles/optimize-long-tasks#manually_defer_code_execution
 */
export function yieldToMain() {
  return new Promise((resolve) => {
    setTimeout(resolve, 0)
  })
}

// export const isProduction = EnvVars.nodeEnvironment === 'production'
