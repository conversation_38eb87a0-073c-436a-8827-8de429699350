import { HttpStatusCodeLiteral } from 'tsoa'
import { EnvVars } from '../constants/env'
import { Response as ExResponse } from 'express'
import { logger } from './logger'

export type InternalResponseReturns<
  T extends Record<string, unknown> | ArrayLike<unknown> | undefined,
> = {
  statusCode: HttpStatusCodeLiteral
  status: 'success' | 'error'
  message: string
  data?: T
}

export type createHTTPResponseReturns<
  T extends Record<string, unknown> | ArrayLike<unknown> | undefined,
> = {
  status: 'success' | 'error'
  message: string
  data?: T
}

export const internalResponse = <T = undefined>(
  statusCode: HttpStatusCodeLiteral,
  status: 'success' | 'error',
  message: string,
  data?: T,
) => {
  return {
    status,
    statusCode,
    message,
    data,
  }
}

export const createHTTPResponse = <
  T extends Record<string, unknown> | ArrayLike<unknown> | undefined,
>(
  res: ExResponse,
  body: InternalResponseReturns<T>,
) => {
  const { status, statusCode, message, data = {} } = body

  const responseObject: Partial<createHTTPResponseReturns<T>> = {}

  const includeData = data
    ? Array.isArray(data)
      ? data.length > 0
      : Object.entries(data).length > 0
    : false

  responseObject['status'] = status
  responseObject['message'] = message
  responseObject['data'] = data as T

  if (!includeData) delete responseObject['data']
  if (Number(statusCode) === 500) {
    logger(module).error(JSON.stringify(responseObject, null, 2))
  } else {
    logger(module).info(JSON.stringify(responseObject, null, 2))
  }
  return res.status(statusCode).json(responseObject)
}

export const hideDevelopmentMessage = (message: string) => {
  if (EnvVars.nodeEnvironment === 'development') {
    return message
  } else {
    return 'An unknown error occurred. Try again later!'
  }
}
