import {
  Controller,
  Tags,
  Route,
  Patch,
  Security,
  Middlewares,
  Request,
  Body,
  Post,
  SuccessResponse,
  Get,
} from 'tsoa'
import { inject, injectable } from 'tsyringe'
import { UserService } from './user.service'
import { createRateLimiter } from '../../middleware/rate-limiter.middleware'
import { AuthenticatedRequest } from '../../types/req.types'
import {
  ChangeEmailDto,
  ChangeEmailSchema,
  ChangePasswordDto,
  ChangePasswordSchema,
  ChangePhoneDTO,
  ChangePhoneSchema,
  ConfirmPasswordDto,
  ConfirmPasswordSchema,
  VerifyChangeEmailOTP,
  VerifyChangeEmailOTPSchema,
  VerifyChangePhoneDTO,
  VerifyChangePhoneSchema,
} from '../../types/dtos.types'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import express from 'express'

@injectable()
@Route('user')
@Tags('User Profile Controller')
@Middlewares(express.json())
export class UserController extends Controller {
  constructor(@inject('userService') private userService: UserService) {
    super()
  }

  @Get('')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async getUser(@Request() request: AuthenticatedRequest) {
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.userService.getUser(user)
    this.setStatus(response.statusCode)
    return response
  }

  @Patch('change-password')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  @Middlewares(createRateLimiter({ windowMs: 30 * 1000, limit: 1 }))
  public async changePassword(
    @Request() request: AuthenticatedRequest,
    @Body() body: ChangePasswordDto,
  ) {
    const check = await ChangePasswordSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<ChangePasswordDto>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.userService.changePassword(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('confirm-password')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async confirmPassword(
    @Request() request: AuthenticatedRequest,
    @Body() body: ConfirmPasswordDto,
  ) {
    const check = await ConfirmPasswordSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<ConfirmPasswordDto>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }
    const response = await this.userService.confirmPassword(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('change-phone')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async startChangePhoneProcess(
    @Request() request: AuthenticatedRequest,
    @Body() body: ChangePhoneDTO,
  ) {
    const check = await ChangePhoneSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<ChangePhoneDTO>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.userService.startChangePhoneProcess(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('change-phone/verify')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async changePhoneNumber(
    @Request() request: AuthenticatedRequest,
    @Body() body: VerifyChangePhoneDTO,
  ) {
    const check = await VerifyChangePhoneSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<VerifyChangePhoneDTO>(
        check.error,
        'body',
      )
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.userService.changePhoneNumber(body, user)
    this.setStatus(response.statusCode)
    return response
  }
  @Get('change-email/initiate')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async sendEmailOTP(@Request() request: AuthenticatedRequest) {
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.userService.sendEmailOTP(user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('change-email/verify-otp')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async verifyEmailOTP(
    @Request() request: AuthenticatedRequest,
    @Body() body: VerifyChangeEmailOTP,
  ) {
    const check = await VerifyChangeEmailOTPSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<VerifyChangeEmailOTP>(
        check.error,
        'body',
      )
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }
    const response = await this.userService.verifyEmailOTP(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('change-email/complete')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async changeEmail(
    @Request() request: AuthenticatedRequest,
    @Body() body: ChangeEmailDto,
  ) {
    const check = await ChangeEmailSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<ChangeEmailDto>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }
    const response = await this.userService.changeEmail(body, user)
    this.setStatus(response.statusCode)
    return response
  }
}
