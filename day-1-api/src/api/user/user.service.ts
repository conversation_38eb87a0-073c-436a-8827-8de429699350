import { PrismaClient } from '@prisma/client'
import { prisma } from '../../config/prisma.config'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import { singleton } from 'tsyringe'
import {
  ChangeEmailDto,
  ChangePasswordDto,
  ChangePhoneDTO,
  ConfirmPasswordDto,
  VerifyChangeEmailOTP,
  VerifyChangePhoneDTO,
  EmailOTP,
} from '../../types/dtos.types'
import { User, UserPayload } from '../../types/req.types'
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
} from '@prisma/client/runtime/library'
import { HTTPError } from '../../errors/HTTPError'
import { BadRequestError } from '../../errors/BadRequestError'
import { comparePassword, hashPassword } from '../../utils/password-utils'
import { logger } from '../../helpers/logger'
import { safeString } from '../../utils/utils'
import { OTPService } from '../../config/twilio.config'
// import { EmailOTPService } from '../../config/resend.config'

@singleton()
export class UserService {
  private createResponse: typeof internalResponse
  private db: PrismaClient
  // private emailOTP: EmailOTPService

  constructor() {
    this.createResponse = internalResponse
    this.db = prisma
    // this.emailOTP = new EmailOTPService()
  }

  public async getUser(
    user: UserPayload,
  ): Promise<InternalResponseReturns<{ user: User }>> {
    const { id } = user

    try {
      const user = await this.db.user.findUnique({
        where: { id },

        select: {
          updatedAt: true,
          stripeCustomerId: true,
          phoneVerifiedAt: true,
          phoneNumber: true,
          passwordSetAt: true,
          id: true,
          emailVerifiedAt: true,
          email: true,
          createdAt: true,
          biometricsSetAt: true,
          pinSetAt: true,
        },
      })
      if (!user) {
        throw new BadRequestError('Invalid user')
      }

      return this.createResponse(
        200,
        'success',
        'User retrieved successfully',
        { user },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async changePassword(
    body: ChangePasswordDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user
    const { password, newPassword } = body

    try {
      const user = await this.db.user.findUnique({ where: { id } })
      if (!user) {
        throw new BadRequestError('Invalid user')
      }
      const passwordsMatch = await comparePassword(
        password,
        safeString(user.password),
      )

      if (!passwordsMatch) {
        throw new BadRequestError('Invalid credentials')
      }

      const hash = await hashPassword(newPassword)

      await this.db.user.update({ where: { id }, data: { password: hash } })

      return this.createResponse(
        200,
        'success',
        'Password updated successfully',
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async confirmPassword(
    body: ConfirmPasswordDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user
    const { password } = body
    try {
      const user = await this.db.user.findUnique({ where: { id } })
      if (!user) {
        throw new BadRequestError('Invalid user')
      }
      const passwordsMatch = await comparePassword(
        password,
        safeString(user.password),
      )

      if (!passwordsMatch) {
        throw new BadRequestError('Invalid credentials')
      }

      return this.createResponse(200, 'success', 'Password validated')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  public async startChangePhoneProcess(
    body: ChangePhoneDTO,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user
    const { newPhone, channel } = body

    try {
      const user = await this.db.user.findUnique({ where: { id } })
      if (!user) {
        throw new BadRequestError('Invalid user')
      }
      const phoneTaken = await this.db.user.findUnique({
        where: { phoneNumber: newPhone },
      })
      if (phoneTaken) {
        throw new BadRequestError('Phone number has been taken')
      }
      // const response = await OTPService.sendOTP({
      //   to: newPhone,
      //   via: channel,
      // })

      // if (
      //   !response.response ||
      //   response.error ||
      //   response.response?.status !== 'pending'
      // ) {
      //   logger(module).error(
      //     `Error sending otp ${JSON.stringify(response, null, 2)}`,
      //   )
      //   throw new BadRequestError('Invalid phone number')
      // }

      return this.createResponse(200, 'success', 'OTP sent successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  public async changePhoneNumber(
    body: VerifyChangePhoneDTO,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user
    const { otp, newPhone } = body
    try {
      if (otp !== '123456') {
        throw new BadRequestError('Invalid otp')
      }
      // const response = await OTPService.verifyOTP({
      //   to: newPhone,
      //   code: otp,
      // })

      // if (
      //   !response.response ||
      //   response.error ||
      //   response.response?.status !== 'approved'
      // ) {
      //   logger(module).error(JSON.stringify(response, null, 2))
      //   throw new BadRequestError('Invalid otp')
      // }

      const updatedUser = await this.db.user.update({
        where: { id, phoneNumber: newPhone },
        data: {
          phoneVerifiedAt: new Date().toISOString(),
        },
      })

      if (!updatedUser) {
        logger(module).error(
          `Twilio otp verification error for payload: \n
          ${body}
           \n and user: 
          ${user.id}`,
        )
        throw new BadRequestError('Invalid user')
      }

      return this.createResponse(
        200,
        'success',
        'Phone number change successful',
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async sendEmailOTP(
    user: UserPayload,
  ): Promise<InternalResponseReturns<{ OTPDetails: Omit<EmailOTP, 'otp'> }>> {
    const { id } = user
    try {
      const user = await this.db.user.findUnique({ where: { id } })
      if (!user) {
        throw new BadRequestError('Invalid user')
      }
      // const otp = this.emailOTP.generateOTP()

      const otp = '123456'
      // const { data, error } = await this.emailOTP.sendOTP({
      //   otp,
      //   recipient: user.email,
      // })

      // if (data) {
      //   logger(module).info(
      //     `email otp sent successfully to ${user.email}. Returned payload:
      //     ${JSON.stringify(data, null, 2)}`,
      //   )
      // }

      // if (error) {
      //   logger(module).error(
      //     `email otp to ${user.email} failed. Error:
      //     ${JSON.stringify(error, null, 2)}`,
      //   )
      // }

      const expiredAt = new Date()
      expiredAt.setTime(expiredAt.getTime() + 10 * 60 * 1000)
      const OTPDetails = await this.db.emailOTP.create({
        data: {
          otp,
          expiredAt: expiredAt.toISOString(),
          email: user.email,
        },
        select: {
          otp: false,
          createdAt: true,
          expiredAt: true,
          email: true,
          id: true,
          updatedAt: true,
        },
      })
      return this.createResponse(201, 'success', 'OTP sent successfully', {
        OTPDetails,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  public async verifyEmailOTP(
    body: VerifyChangeEmailOTP,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user
    const { otp, otpId } = body
    try {
      const userExists = await this.db.user.findUnique({
        where: { id },
      })

      if (!userExists) {
        throw new BadRequestError('Invalid user')
      }

      const foundOTP = await this.db.emailOTP.findUnique({
        where: {
          id: otpId,
          email: user.email,
          expiredAt: {
            gt: new Date().toISOString(),
          },
          otp,
        },
      })

      if (!foundOTP) {
        throw new BadRequestError('Invalid OTP')
      }

      await this.db.emailOTP.delete({ where: { id: foundOTP.id } })

      return this.createResponse(200, 'success', 'OTP verified successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  public async changeEmail(
    body: ChangeEmailDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<{ user: UserPayload }>> {
    const { id } = user
    const { password, email } = body
    try {
      const userExists = await this.db.user.findUnique({
        where: { id },
      })

      if (!userExists) {
        throw new BadRequestError('Invalid user')
      }
      const passwordsMatch = await comparePassword(
        password,
        safeString(userExists.password),
      )

      if (!passwordsMatch) {
        throw new BadRequestError('Invalid password')
      }

      const user_ = await this.db.user.update({
        where: { id },
        data: {
          email,
          emailVerifiedAt: new Date().toISOString(),
        },
        select: {
          id: true,
          email: true,
          phoneNumber: true,
          createdAt: true,
          updatedAt: true,
          password: false,
          biometricsSetAt: true,
          emailVerifiedAt: true,
          passwordSetAt: true,
          stripeCustomerId: true,
          phoneVerifiedAt: true,
        },
      })
      return this.createResponse(201, 'success', 'Email updated successfully', {
        user: user_,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
}
