import {
  Controller,
  Route,
  Post,
  SuccessResponse,
  Body,
  Tags,
  Security,
  Request,
  Middlewares,
  Patch,
} from 'tsoa'
import { inject, injectable } from 'tsyringe'
import { OnboardingService } from './onboarding.service'
import {
  AddPasswordBodyDto,
  AddPasswordBodySchema,
  AddPhoneDto,
  AddPhoneNumberSchema,
  AddPin,
  AddPinSchema,
  CreateUserBodyDto,
  CreateUserBodySchema,
  VerifyEmailBodyDto,
  VerifyEmailBodySchema,
  VerifyPhoneDto,
  VerifyPhoneNumberSchema,
} from '../../types/dtos.types'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import { createRateLimiter } from '../../middleware/rate-limiter.middleware'
import { AuthenticatedRequest } from '../../types/req.types'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import express from 'express'

@injectable()
@Route('')
@Tags('Onboarding Controller')
@Middlewares(express.json())
export class OnboardingController extends Controller {
  constructor(
    @inject('onboardingService') private onboardingService: OnboardingService,
  ) {
    super()
  }

  @Post('register')
  @SuccessResponse('201', 'Created')
  public async register(@Body() body: CreateUserBodyDto) {
    const check = await CreateUserBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<CreateUserBodyDto>(check.error, 'body')
    }
    const response = await this.onboardingService.registerUser(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('resend-email-otp')
  @SuccessResponse('200', 'Ok')
  @Middlewares(
    createRateLimiter({
      windowMs: 30 * 1000,
      limit: 1,
      skipFailedRequests: true,
    }),
  )
  public async resendEmailOTP(@Body() body: CreateUserBodyDto) {
    const check = await CreateUserBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<CreateUserBodyDto>(check.error, 'body')
    }
    const response = await this.onboardingService.resendEmailOTP(body)
    this.setStatus(response.statusCode)
    return response
  }
  @Patch('verify-email')
  @SuccessResponse('200', 'Ok')
  public async verifyEmail(@Body() body: VerifyEmailBodyDto) {
    const check = await VerifyEmailBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<VerifyEmailBodyDto>(check.error, 'body')
    }

    const response = await this.onboardingService.verifyEmailOTP(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Patch('add-password')
  @Security('jwt')
  @SuccessResponse('200', 'Ok')
  public async addPassword(
    @Request() request: AuthenticatedRequest,
    @Body() body: AddPasswordBodyDto,
  ) {
    const check = await AddPasswordBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<AddPasswordBodyDto>(check.error, 'body')
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }
    const response = await this.onboardingService.addPassword(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Patch('add-phone')
  @SuccessResponse('200', 'OK')
  @Security('jwt')
  public async addPhone(
    @Request() request: AuthenticatedRequest,
    @Body() body: AddPhoneDto,
  ) {
    const check = await AddPhoneNumberSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<AddPhoneDto>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.onboardingService.addPhone(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('resend-otp')
  @SuccessResponse('200', 'OK')
  @Security('jwt')
  @Middlewares(createRateLimiter({ windowMs: 30 * 1000, limit: 1 }))
  public async resendOTP(
    @Request() request: AuthenticatedRequest,
    @Body() body: AddPhoneDto,
  ) {
    const check = await AddPhoneNumberSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<AddPhoneDto>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.onboardingService.resendOTP(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Patch('verify-phone')
  @SuccessResponse('200', 'Created')
  @Security('jwt')
  public async verifyPhone(
    @Request() request: AuthenticatedRequest,
    @Body() body: VerifyPhoneDto,
  ) {
    const check = await VerifyPhoneNumberSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<VerifyPhoneDto>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.onboardingService.verifyPhone(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('enable-biometrics')
  @SuccessResponse('200', 'Ok')
  @Security('jwt')
  public async enableBiometrics(@Request() request: AuthenticatedRequest) {
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.onboardingService.enableBiometrics(user)
    this.setStatus(response.statusCode)
    return response
  }

  @Patch('add-pin')
  @SuccessResponse('200', 'Ok')
  @Security('jwt')
  public async addPin(
    @Request() request: AuthenticatedRequest,
    @Body() body: AddPin,
  ) {
    const check = await AddPinSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<AddPin>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.onboardingService.addPin(user, body)
    this.setStatus(response.statusCode)
    return response
  }
}
