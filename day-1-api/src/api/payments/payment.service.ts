import { singleton } from 'tsyringe'
import { prisma } from '../../config/prisma.config'
import { PaymentsManager } from '../../config/stripe.config'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import { PrismaClient } from '@prisma/client'
import { transactionType, UserPayload } from '../../types/req.types'
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
} from '@prisma/client/runtime/library'
import { BadRequestError } from '../../errors/BadRequestError'
import { HTTPError } from '../../errors/HTTPError'
import { logger } from '../../helpers/logger'
import Stripe from 'stripe'
import { InitializePaymentIntentBodyDto } from '../../types/dtos.types'
import { safeString } from '../../utils/utils'
import { IMTOService } from '../../config/imto.config'

@singleton()
export class PaymentService {
  private createResponse: typeof internalResponse
  private db: PrismaClient
  private paymentsManager: PaymentsManager
  private IMTOService: IMTOService
  constructor() {
    this.createResponse = internalResponse
    this.db = prisma
    this.paymentsManager = new PaymentsManager()
    this.IMTOService = new IMTOService()
  }

  private async getStripeTransactionStatus(status: Stripe.Charge.Status) {
    const pendingStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'pending' },
      create: {
        statusName: 'pending',
      },
      update: {},
    })
    const successStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'success' },
      create: { statusName: 'success' },
      update: {},
    })
    const failureStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'failed' },
      create: {
        statusName: 'failed',
      },
      update: {},
    })

    if (status === 'succeeded') {
      return successStatus
    }
    if (status === 'failed') {
      return failureStatus
    }

    return pendingStatus
  }

  private async getTransactionType(type: transactionType) {
    return await this.db.transactionType.upsert({
      where: { typeName: type },
      create: {
        typeName: type,
      },
      update: {},
    })
  }

  public async initializeStripePaymentIntent(
    body: InitializePaymentIntentBodyDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<any>> {
    const { id } = user
    const { amount } = body
    /**
     * TODO: add kyc restrictions
     */
    try {
      let user_ = await this.db.user.findUnique({
        where: { id },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }
      await this.db.userWallet.upsert({
        create: { userId: user_.id },
        update: {},
        where: { userId: user_.id },
      })
      if (!user_.stripeCustomerId) {
        const { data, error } = await this.paymentsManager.createCustomer(
          user_.email,
        )

        if (error || !data) {
          logger(module).error(JSON.stringify(error, null, 2))

          throw new BadRequestError('Unable to add card, try again later')
        }
        logger(module).info(JSON.stringify(data, null, 2))

        user_ = await this.db.user.update({
          where: { id },
          data: { stripeCustomerId: data.id },
          include: { Wallet: true },
        })
      }

      const { data, error } =
        await this.paymentsManager.initializePaymentIntent({
          amount: amount,
          customerId: safeString(user_?.stripeCustomerId),
          userId: id,
        })

      if (error || !data) {
        logger(module).error(
          `Payment Intent error: ${JSON.stringify(error, null, 2)}`,
        )
        throw new BadRequestError(
          'Unable to initialize payment, try again later',
        )
      }

      logger(module).info(
        `Payment Intent success: ${JSON.stringify(data, null, 2)}`,
      )
      const {
        amount: amount_,
        id: paymentIntentId,
        client_secret,
        status,
      } = data
      const exchangeRate = this.IMTOService.getExchangeRate()

      const transactionType = await this.getTransactionType('credit')

      const transactionStatus = await this.getStripeTransactionStatus('pending')

      const txn = await this.db.transaction.create({
        data: {
          userId: id,
          transactionId: paymentIntentId,
          /**
           * ! Wallet value in dollar
           */
          transactionAmount: amount_.toFixed(2),

          /**
           * ! Wallet value in naira
           */
          baseTransactionAmount: (amount_ * exchangeRate.value).toFixed(2),
          exchangeRate,
          transactionStatusId: transactionStatus.id,
          transactionTypeId: transactionType.id,
          stripeClientSecret: client_secret,
          stripeTransactionStatus: status,
        },
        include: {
          transactionStatus: true,
          transactionType: true,
        },
      })

      logger(module).info(
        `Transaction created successfully success: ${JSON.stringify(txn, null, 2)}`,
      )

      return this.createResponse(
        201,
        'success',
        'Payment intent initialized successfully',

        {
          ...txn,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async retrievePayment(
    transactionId: string,
    user: UserPayload,
  ): Promise<InternalResponseReturns<any>> {
    const { id } = user

    try {
      const user_ = await this.db.user.findUnique({
        where: { id },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }

      const txn = await this.db.transaction.findUnique({
        where: {
          id: +transactionId,
          userId: id,
        },
        include: {
          transactionStatus: true,
          transactionType: true,
        },
      })

      if (!txn) {
        throw new BadRequestError('Transaction not found')
      }

      return this.createResponse(
        200,
        'success',
        'Payment returned successfully',

        {
          ...txn,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async getWallet(
    user: UserPayload,
  ): Promise<InternalResponseReturns<any>> {
    const { id } = user
    try {
      const user_ = await this.db.user.findUnique({
        where: { id },
        include: { Wallet: true },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }

      let wallet = user_.Wallet

      if (!wallet) {
        wallet = (
          await this.db.user.update({
            where: { id },
            data: { Wallet: { create: {} } },
            include: { Wallet: true },
          })
        ).Wallet
      }

      if (!wallet) {
        throw new BadRequestError('Unable to retrieve wallet')
      }
      return this.createResponse(
        200,
        'success',
        'Wallet details retrieved successfully',
        {
          wallet: {
            userId: wallet.userId,
            id: wallet.id,
            updatedAt: wallet.updatedAt,
            createdAt: wallet.createdAt,
            balance: wallet.balance.toFixed(2),
            baseBalance: wallet.baseBalance.toFixed(2),
          },
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
}
