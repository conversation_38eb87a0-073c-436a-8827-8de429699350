import { inject, injectable } from 'tsyringe'
import {
  Controller,
  Route,
  SuccessResponse,
  Tags,
  Security,
  Request,
  Get,
  Query,
  Middlewares,
} from 'tsoa'
import { AuthenticatedRequest } from '../../types/req.types'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import { BanksService } from './banks.service'
import {
  PredictAccountDetailsDto,
  PredictAccountDetailsSchema,
  VerifyAccountDetailsDto,
  VerifyAccountDetailsSchema,
} from '../../types/dtos.types'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import express from 'express'

@injectable()
@Tags('Nuban Controller')
@Route('banks')
@Middlewares(express.json())
export class BanksController extends Controller {
  constructor(@inject('banksService') private paymentsService: BanksService) {
    super()
  }

  @Get('predict')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async PredictAccountDetails(
    @Request() request: AuthenticatedRequest,
    @Query() accountNumber: string,
  ) {
    const check = await PredictAccountDetailsSchema.safeParseAsync({
      accountNumber,
    })
    if (!check.success) {
      throw new ZodErrorToValidateError<PredictAccountDetailsDto>(
        check.error,
        'query',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.paymentsService.predictBank({
      accountNumber,
    })
    this.setStatus(response.statusCode)
    return response
  }
  @Get('verify')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async VerifyAccountDetails(
    @Request() request: AuthenticatedRequest,
    @Query() accountNumber: string,
    @Query() bankCode: string,
  ) {
    const check = await VerifyAccountDetailsSchema.safeParseAsync({
      accountNumber,
      bankCode,
    })
    if (!check.success) {
      throw new ZodErrorToValidateError<VerifyAccountDetailsDto>(
        check.error,
        'query',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.paymentsService.verifyAccountDetails({
      accountNumber,
      bankCode,
    })
    this.setStatus(response.statusCode)
    return response
  }

  @Get('list')
  @SuccessResponse('200', 'OK')
  public async GetBanks() {
    const response = await this.paymentsService.getBanks()
    this.setStatus(response.statusCode)
    return response
  }

  @Get('rate')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async GetExchangeRate(@Request() request: AuthenticatedRequest) {
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.paymentsService.getExchangeRate()
    this.setStatus(response.statusCode)
    return response
  }
}
