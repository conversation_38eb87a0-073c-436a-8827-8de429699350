import { singleton } from 'tsyringe'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import {
  PredictAccountDetailsDto,
  VerifyAccountDetailsDto,
} from '../../types/dtos.types'
import { NUBANService } from '../../config/nuban.config'
import { BadRequestError } from '../../errors/BadRequestError'
import { PaystackService } from '../../config/paystack.config'
import { NubanBank, PaystackBank } from '../../types/req.types'
import { safeArray } from '../../utils/utils'
import { IMTOService } from '../../config/imto.config'

@singleton()
export class BanksService {
  private NubanService: NUBANService
  private createResponse: typeof internalResponse
  private paystackService: PaystackService
  private IMTOService: IMTOService

  constructor() {
    this.IMTOService = new IMTOService()
    this.NubanService = new NUBANService()
    this.createResponse = internalResponse
    this.paystackService = new PaystackService()
  }

  public async predictBank(
    pathParams: PredictAccountDetailsDto,
  ): Promise<InternalResponseReturns<any>> {
    const { data, error } =
      await this.NubanService.predictAccountNumber(pathParams)

    if (error) {
      if (error instanceof Error) {
        throw new BadRequestError(error.message)
      }
      throw new BadRequestError('Unable to verify account')
    }

    return this.createResponse(
      200,
      'success',
      'Bank details fetched successfully',
      {
        possibleBanks: data,
      },
    )
  }

  public async verifyAccountDetails(
    pathParams: VerifyAccountDetailsDto,
  ): Promise<InternalResponseReturns<any>> {
    const { data, error } =
      await this.NubanService.verifyAccountNumber(pathParams)

    if (error || !data) {
      throw new BadRequestError('Unable to verify account')
    }

    return this.createResponse(
      200,
      'success',
      'Bank details fetched successfully',
      {
        data,
      },
    )
  }

  private nubanBankToPaystackBankDetails = (
    data: PaystackBank,
  ): Omit<PaystackBank, 'name' | 'code'> & NubanBank => {
    const { name, ...rest } = data
    return {
      ...rest,
      bank_name: name,
    }
  }

  private removeBanksThatDoNotSupportTransfers = (
    data: PaystackBank,
  ): boolean => {
    return data.supports_transfer === true && data.active === true
  }
  public async getBanks(): Promise<
    InternalResponseReturns<{
      banks: (Omit<PaystackBank, 'name' | 'code'> & NubanBank)[]
    }>
  > {
    const { data, error } = await this.paystackService.getAllBanks()

    if (error) {
      throw new BadRequestError('Unable to fetch banks')
    }
    return this.createResponse(
      200,
      'success',
      'Bank details fetched successfully',
      {
        banks: safeArray<PaystackBank>(data?.data)
          .filter(this.removeBanksThatDoNotSupportTransfers)
          .map(this.nubanBankToPaystackBankDetails),
      },
    )
  }

  public async getExchangeRate(): Promise<
    InternalResponseReturns<{
      value: number
      timeStamp: string
    }>
  > {
    const exchangeRate = this.IMTOService.getExchangeRate()

    return this.createResponse(
      200,
      'success',
      'Bank details fetched successfully',
      {
        ...exchangeRate,
      },
    )
  }
}
