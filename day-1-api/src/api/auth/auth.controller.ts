import {
  Controller,
  Route,
  Post,
  SuccessResponse,
  Body,
  Tags,
  Security,
  Request,
  Patch,
  Middlewares,
} from 'tsoa'
import { inject, injectable } from 'tsyringe'
import { AuthService } from './auth.service'
import {
  CreateUserBodyDto,
  CreateUserBodySchema,
  LoginDto,
  loginSchema,
  twoFADto,
  twoFASchema,
  ResetPasswordDto,
  ResetPasswordSchema,
  RefreshTokenDto,
  RefreshTokenSchema,
  pinLoginSchema,
  PinLoginDto,
  PublicKeyLoginDto,
  PublicKeyBodySchema,
} from '../../types/dtos.types'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import { AuthenticatedRequest } from '../../types/req.types'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import express from 'express'

@injectable()
@Route('auth')
@Tags('Authentication Controller')
@Middlewares(express.json())
export class AuthController extends Controller {
  constructor(@inject('authService') private authService: AuthService) {
    super()
  }

  @Post('login')
  @SuccessResponse('201', 'Created')
  public async login(@Body() body: LoginDto) {
    const check = await loginSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<LoginDto>(check.error, 'body')
    }
    const response = await this.authService.login(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('refresh-token')
  @SuccessResponse('200', 'OK')
  public async refresh(@Body() body: RefreshTokenDto) {
    const check = await RefreshTokenSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<RefreshTokenDto>(check.error, 'body')
    }
    const response = await this.authService.refreshTokens(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('two-fa')
  @SuccessResponse('200')
  @Security('jwt')
  public async twoFA(
    @Request() request: AuthenticatedRequest,
    @Body() body: twoFADto,
  ) {
    const check = await twoFASchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<twoFADto>(check.error, 'body')
    }

    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.authService.twoFA(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('public-key-login')
  @SuccessResponse('200', 'OK')
  public async publicKeyLogin(@Body() body: PublicKeyLoginDto) {
    const check = await PublicKeyBodySchema.safeParseAsync(body)

    if (!check.success) {
      throw new ZodErrorToValidateError<PublicKeyLoginDto>(check.error, 'body')
    }

    const response = await this.authService.publicKeyLogin(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('pin-login')
  @SuccessResponse('200', 'OK')
  public async pinLogin(@Body() body: PinLoginDto) {
    const check = await pinLoginSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<PinLoginDto>(check.error, 'body')
    }

    const response = await this.authService.pinLogin(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('forgot-password')
  @SuccessResponse('200')
  public async forgotPassword(@Body() body: CreateUserBodyDto) {
    const check = await CreateUserBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<CreateUserBodyDto>(check.error, 'body')
    }

    const response = await this.authService.forgotPassword(body)
    this.setStatus(response.statusCode)
    return response
  }

  @Patch('reset-password')
  @SuccessResponse('200')
  public async resetPassword(@Body() body: ResetPasswordDto) {
    const check = await ResetPasswordSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<ResetPasswordDto>(check.error, 'body')
    }

    const response = await this.authService.resetPassword(body)
    this.setStatus(response.statusCode)
    return response
  }
}
