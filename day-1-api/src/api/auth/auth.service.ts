import { singleton } from 'tsyringe'
import { prisma } from '../../config/prisma.config'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import { PrismaClient } from '@prisma/client'
import {
  CreateUserBodyDto,
  LoginDto,
  twoFADto,
  ResetPasswordDto,
  RefreshTokenDto,
  PinLoginDto,
  PublicKeyLoginDto,
} from '../../types/dtos.types'
import { hashPassword, comparePassword } from '../../utils/password-utils'
import { JwtService, TokenDetails, UserPayloadFromUser } from '../../utils/auth'
import { OTPService } from '../../config/twilio.config'
import { BadRequestError } from '../../errors/BadRequestError'
import { logger } from '../../helpers/logger'
import { BiometricsKeyPayload, User, UserPayload } from '../../types/req.types'
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
} from '@prisma/client/runtime/library'
import { HTTPError } from '../../errors/HTTPError'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
// import { EmailOTPService } from '../../config/resend.config'
import { safeString } from '../../utils/utils'
import { PaymentsManager } from '../../config/stripe.config'
import { BiometricKeyService } from '../../services/biometric-key.service'
import { EnvVars } from '../../constants/env'

@singleton()
export class AuthService {
  private createResponse: typeof internalResponse
  private db: PrismaClient
  // private emailOTP: EmailOTPService
  private paymentsManager: PaymentsManager
  private biometricsKeyService: BiometricKeyService

  constructor() {
    this.createResponse = internalResponse
    this.db = prisma
    this.paymentsManager = new PaymentsManager()
    // this.emailOTP = new EmailOTPService()
    this.biometricsKeyService = new BiometricKeyService({
      encryptionKey: EnvVars.bioSecret,
    })
  }

  public async login(body: LoginDto): Promise<
    InternalResponseReturns<{
      user: User
      tokens: {
        accessToken: TokenDetails
        refreshToken: TokenDetails
      }
      publicKey: string | null
    }>
  > {
    try {
      const { email, password } = body

      const user = await this.db.user.findUnique({
        where: { email },
      })
      if (!user || !user.passwordSetAt || !user.emailVerifiedAt) {
        throw new UnauthenticatedError('Invalid credentials')
      }
      const passwordsMatch = await comparePassword(
        password,
        safeString(user.password),
      )
      if (!passwordsMatch) {
        throw new UnauthenticatedError('Invalid credentials')
      }
      if (user.phoneNumber) {
        // await OTPService.sendOTP({ to: user.phoneNumber, via: 'sms' })
      }
      const accessToken = JwtService.generateToken(UserPayloadFromUser(user))
      const refreshToken = JwtService.generateToken(
        UserPayloadFromUser(user),
        'refresh',
      )
      const publicKey = user.biometricsSetAt
        ? this.biometricsKeyService.generateToken<BiometricsKeyPayload>({
            email: user.email,
            phoneNumber: user.phoneNumber,
          })
        : null

      return this.createResponse(201, 'success', 'Login successful', {
        user: {
          email: user.email,
          emailVerifiedAt: user.emailVerifiedAt,
          createdAt: user.createdAt,
          biometricsSetAt: user.biometricsSetAt,
          phoneNumber: user.phoneNumber,
          passwordSetAt: user.passwordSetAt,
          id: user.id,
          updatedAt: user.updatedAt,
          phoneVerifiedAt: user.phoneVerifiedAt,
          pinSetAt: user.pinSetAt,
          stripeCustomerId: user.stripeCustomerId,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
        publicKey,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  public async twoFA(
    body: twoFADto,
    user_: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    try {
      const { otp } = body
      const { id } = user_

      const user = await this.db.user.findUnique({
        where: {
          id,
        },
      })
      if (!user) {
        throw new BadRequestError('Invalid user')
      }

      if (!user.phoneNumber || !user.phoneVerifiedAt) {
        throw new BadRequestError('Please verify your phone number')
      }

      if (otp !== '123456') {
        throw new BadRequestError('Invalid otp')
      }

      // const response = await OTPService.verifyOTP({
      //   code: otp,
      //   to: user.phoneNumber,
      // })

      // if (!response.response || response.error) {
      //   logger(module).error(JSON.stringify(response, null, 2))
      //   throw new BadRequestError('Invalid otp')
      // }
      return this.createResponse(200, 'success', 'OTP verified')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  verifyPublicKey(publicKey: string, dataToCompare: BiometricsKeyPayload) {
    const data =
      this.biometricsKeyService.decodeToken<BiometricsKeyPayload>(publicKey)

    if (!data) {
      return false
    }
    if (
      data.email === dataToCompare.email &&
      data.phoneNumber === dataToCompare.phoneNumber
    ) {
      return true
    }

    return false
  }
  public async publicKeyLogin(body: PublicKeyLoginDto): Promise<
    InternalResponseReturns<{
      user: User
      tokens: {
        accessToken: TokenDetails
        refreshToken: TokenDetails
      }
      publicKey: string
    }>
  > {
    const { publicKey, email } = body

    try {
      const user = await this.db.user.findUnique({
        where: { email },
      })

      if (!user) {
        throw new BadRequestError('User not found')
      }

      if (!user.biometricsSetAt) {
        throw new BadRequestError('Biometric auth not set')
      }

      const isPublicKeyValid = this.verifyPublicKey(publicKey, {
        email: user.email,
        phoneNumber: user.phoneNumber,
      })
      if (!isPublicKeyValid) {
        throw new BadRequestError('Invalid Public Key')
      }

      const accessToken = JwtService.generateToken(UserPayloadFromUser(user))
      const refreshToken = JwtService.generateToken(
        UserPayloadFromUser(user),
        'refresh',
      )

      return this.createResponse(200, 'success', 'Biometric login successful', {
        user: {
          email: user.email,
          emailVerifiedAt: user.emailVerifiedAt,
          createdAt: user.createdAt,
          biometricsSetAt: user.biometricsSetAt,
          phoneNumber: user.phoneNumber,
          passwordSetAt: user.passwordSetAt,
          id: user.id,
          updatedAt: user.updatedAt,
          phoneVerifiedAt: user.phoneVerifiedAt,
          pinSetAt: user.pinSetAt,
          stripeCustomerId: user.stripeCustomerId,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
        publicKey,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async pinLogin(body: PinLoginDto): Promise<
    InternalResponseReturns<{
      user: User
      tokens: {
        accessToken: TokenDetails
        refreshToken: TokenDetails
      }
      publicKey: string | null
    }>
  > {
    const { pin, email } = body
    try {
      const user = await this.db.user.findUnique({
        where: { email },
      })

      if (!user) {
        throw new BadRequestError('User not found')
      }

      if (!user.pinSetAt || !user.pin) {
        throw new BadRequestError('Pin not set')
      }

      const isCorrectPin = await comparePassword(pin, user.pin)
      if (!isCorrectPin) {
        throw new BadRequestError('Invalid Pin')
      }

      const accessToken = JwtService.generateToken(UserPayloadFromUser(user))
      const refreshToken = JwtService.generateToken(
        UserPayloadFromUser(user),
        'refresh',
      )
      const publicKey = user.biometricsSetAt
        ? this.biometricsKeyService.generateToken<BiometricsKeyPayload>({
            email: user.email,
            phoneNumber: user.phoneNumber,
          })
        : null
      return this.createResponse(200, 'success', 'Pin login successful', {
        user: {
          email: user.email,
          emailVerifiedAt: user.emailVerifiedAt,
          createdAt: user.createdAt,
          biometricsSetAt: user.biometricsSetAt,
          phoneNumber: user.phoneNumber,
          passwordSetAt: user.passwordSetAt,
          id: user.id,
          updatedAt: user.updatedAt,
          phoneVerifiedAt: user.phoneVerifiedAt,
          pinSetAt: user.pinSetAt,
          stripeCustomerId: user.stripeCustomerId,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
        publicKey,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  public async forgotPassword(
    body: CreateUserBodyDto,
  ): Promise<InternalResponseReturns<undefined>> {
    const { email } = body

    try {
      const user = await this.db.user.findUnique({ where: { email } })

      if (!user) {
        throw new BadRequestError('User not found')
      }

      const tokens = (
        await this.db.emailOTP.findMany({
          where: {
            email,
            expiredAt: {
              gt: new Date().toISOString(),
            },
          },
          orderBy: {
            expiredAt: 'desc',
          },
        })
      ).filter((otp) => otp.otp.length > 6)
      if (tokens.length < 1) {
        // const token = this.emailOTP.generateToken(120)
        const token = '123456'
        // const { data, error } = await this.emailOTP.sendResetToken({
        //   token,
        //   recipient: email,
        // })

        // if (data) {
        //   logger(module).info(
        //     `email otp sent successfully to ${email}. Returned payload:
        //     ${JSON.stringify(data, null, 2)}`,
        //   )
        // }

        // if (error) {
        //   logger(module).error(
        //     `email otp to ${email} failed. Error:  ${JSON.stringify(error, null, 2)}`,
        //   )
        // }

        const expiredAt = new Date()
        expiredAt.setTime(expiredAt.getTime() + 10 * 60 * 1000)
        await this.db.emailOTP.create({
          data: {
            otp: token,
            expiredAt: expiredAt.toISOString(),
            email,
          },
          select: {
            otp: false,
            createdAt: true,
            expiredAt: true,
            email: true,
            id: true,
            updatedAt: true,
          },
        })
        return this.createResponse(
          201,
          'success',
          'Please check your email to continue rest password process',
        )
      }

      const mostRecentToken = tokens[0]

      const redundantTokens = tokens.slice(1)

      for (const token of redundantTokens) {
        await this.db.emailOTP.delete({ where: { id: token.id } })
      }

      // const { data, error } = await this.emailOTP.sendResetToken({
      //   token: mostRecentToken.otp,
      //   recipient: email,
      // })
      // if (data) {
      //   logger(module).info(
      //     `email otp sent successfully to ${email}. Returned payload: ${JSON.stringify(data, null, 2)} `,
      //   )
      // }

      // if (error) {
      //   logger(module).error(
      //     `email otp to ${email} failed. Error:  ${JSON.stringify(error, null, 2)}`,
      //   )
      // }
      const expiredAt = new Date()
      expiredAt.setTime(expiredAt.getTime() + 10 * 60 * 1000)

      await this.db.emailOTP.update({
        where: { id: mostRecentToken.id },
        data: {
          otp: '123456',
          expiredAt: expiredAt.toISOString(),
          email,
        },
      })
      return this.createResponse(200, 'success', 'OTP resent successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async resetPassword(
    body: ResetPasswordDto,
  ): Promise<InternalResponseReturns<undefined>> {
    const { resetToken, password, email } = body
    try {
      const userExists = await this.db.user.findUnique({
        where: { email },
      })

      if (!userExists) {
        throw new BadRequestError('Invalid email')
      }

      const foundOTP = await this.db.emailOTP.findFirst({
        where: {
          email,
          expiredAt: {
            gt: new Date().toISOString(),
          },
          otp: resetToken,
        },
      })

      if (!foundOTP) {
        throw new BadRequestError('Invalid OTP')
      }
      const newPassword = await hashPassword(password)

      await this.db.user.update({
        where: { email },
        data: {
          password: newPassword,
          passwordSetAt: new Date().toISOString(),
        },
      })
      await this.db.emailOTP.delete({ where: { id: foundOTP.id } })
      return this.createResponse(
        200,
        'success',
        'Password reset successfully, please login',
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  public async refreshTokens(
    body: RefreshTokenDto,
    // user: UserPayload,
  ): Promise<
    InternalResponseReturns<{
      tokens: {
        accessToken: TokenDetails
        refreshToken: TokenDetails
      }
    }>
  > {
    // const { id } = user
    const { refreshToken } = body

    const payload = JwtService.verifyToken(refreshToken, 'refresh')
    try {
      if (!payload.verified || !payload.user || payload.error) {
        throw new UnauthenticatedError('Invalid refresh token')
      }

      const user_ = await this.db.user.findUnique({
        where: { id: payload.user.id },
      })

      if (!user_) {
        throw new UnauthenticatedError('Invalid user')
      }

      const accessToken = JwtService.generateToken(payload.user)
      const refreshToken = JwtService.generateToken(payload.user, 'refresh')

      return this.createResponse(200, 'success', 'Token refresh successful', {
        tokens: {
          accessToken,
          refreshToken,
        },
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
}
