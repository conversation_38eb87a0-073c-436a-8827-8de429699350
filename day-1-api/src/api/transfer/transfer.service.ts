import { singleton } from 'tsyringe'

import { prisma } from '../../config/prisma.config'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import { PrismaClient } from '@prisma/client'
import { PaystackService } from '../../config/paystack.config'
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
} from '@prisma/client/runtime/library'
import { BadRequestError } from '../../errors/BadRequestError'
import { HTTPError } from '../../errors/HTTPError'
import { logger } from '../../helpers/logger'
import {
  InitiateTransferDto,
  PaginatedBeneficiaryQueryDto,
  ToggleFavoriteBeneficiariesBodyDTO,
} from '../../types/dtos.types'
import {
  transactionType,
  UserBeneficiaries,
  UserPayload,
} from '../../types/req.types'
import Stripe from 'stripe'
import { AuthService } from '../auth/auth.service'
import { comparePassword } from '../../utils/password-utils'
import { IMTOService } from '../../config/imto.config'
import { getRandomValues, randomUUID } from 'crypto'
import { getPagination, paginationData } from '../../utils/pagination'
import { safeString } from '../../utils/utils'

@singleton()
export class TransfersService {
  private createResponse: typeof internalResponse
  private db: PrismaClient
  private paystackService: PaystackService
  private authService: AuthService
  private IMTOService: IMTOService
  constructor() {
    this.createResponse = internalResponse
    this.db = prisma
    this.paystackService = new PaystackService()
    this.authService = new AuthService()
    this.IMTOService = new IMTOService()
  }

  private async getTransactionType(type: transactionType) {
    return await this.db.transactionType.upsert({
      where: { typeName: type },
      create: {
        typeName: type,
      },
      update: {},
    })
  }
  private async getTransactionStatus(status: Stripe.Charge.Status) {
    const pendingStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'pending' },
      create: {
        statusName: 'pending',
      },
      update: {},
    })
    const successStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'success' },
      create: { statusName: 'success' },
      update: {},
    })
    const failureStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'failed' },
      create: {
        statusName: 'failed',
      },
      update: {},
    })

    if (status === 'succeeded') {
      return successStatus
    }
    if (status === 'failed') {
      return failureStatus
    }

    return pendingStatus
  }

  public async initiateTransfer(
    body: InitiateTransferDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<any>> {
    const { id } = user
    const { amount, reason, accountNumber, bankCode, name } = body
    try {
      const user_ = await this.db.user.findUnique({
        where: { id },
        include: { Wallet: true },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }

      if (!user_.Wallet) {
        throw new BadRequestError('Wallet not found')
      }

      if (user_.Wallet.baseBalance.lt(amount)) {
        throw new BadRequestError('Insufficient Balance')
      }

      if ('pin' in body) {
        if (!user_.pinSetAt || !user_.pin) {
          throw new BadRequestError('Pin not set')
        }
        const isCorrectPin = await comparePassword(body.pin, user_.pin)
        if (!isCorrectPin) {
          throw new BadRequestError('Invalid Pin')
        }
      }

      if ('publicKey' in body) {
        if (!user_.biometricsSetAt) {
          throw new BadRequestError('Biometric auth not set')
        }
        const isPublicKeyValid = this.authService.verifyPublicKey(
          body.publicKey,
          {
            email: user_.email,
            phoneNumber: user_.phoneNumber,
          },
        )
        if (!isPublicKeyValid) {
          throw new BadRequestError('Invalid Public Key')
        }
      }

      /**
       * TODO: add kyc restrictions
       */

      let recipient = await this.db.beneficiaryDetails.findFirst({
        where: {
          accountName: name,
          bankCode,
          accountNumber,
        },
      })

      if (!recipient) {
        const { data, error } = await this.paystackService.createRecipient({
          accountNumber,
          bankCode,
          name,
        })

        if (!data || error) {
          logger(module).error(
            `Unable to create paystack recipient with error: ${JSON.stringify(Error, null, 2)}`,
          )
          throw new BadRequestError('Unable to create recipient')
        }

        const {
          data: {
            recipient_code,
            details: { account_name, account_number, bank_code, bank_name },
          },
        } = data

        recipient = await this.db.beneficiaryDetails.create({
          data: {
            accountName: account_name,
            bankCode: bank_code,
            paystackRecipientId: recipient_code,
            bankName: bank_name,
            accountNumber: account_number,
          },
        })
      }

      const { paystackRecipientId } = recipient

      /**
       * ! PAYSTACK API CALL. RESTORE WHEN I RECEIVE CREDENTIALS
       */
      // const transferObject = await this.paystackService.initializeTransfer({
      //   reason: safeString(reason),
      //   amount: +amount,
      //   recipient: paystackRecipientId,
      // })

      // if (!transferObject.data || transferObject.error) {
      //   logger(module).error(
      //     `Error initializing paystack transfer: ${JSON.stringify(transferObject, null, 2)}`,
      //   )
      //   throw new BadRequestError('Unable to initialize transfer')
      // }

      // logger(module).info(
      //   `Paystack transfer initialized successfully with data: ${JSON.stringify(transferObject.data, null, 2)}`,
      // )

      // const {
      //   data: {
      //     data: { transfer_code, id: transferId, amount: amount_, reference },
      //   },
      // } = transferObject

      // const transactionType = await this.getTransactionType('transfer')
      // const transactionStatus = await this.getTransactionStatus('pending')

      /**
       * ! END OF PAYSTACK API CALL. RESTORE WHEN I RECEIVE CREDENTIALS
       */

      const exchangeRate = this.IMTOService.getExchangeRate()

      let beneficiary = await this.db.userBeneficiaries.findFirst({
        where: {
          userId: id,
          BeneficiaryDetails: {
            accountName: name,
            bankCode,
            accountNumber,
          },
        },
      })

      if (!beneficiary) {
        beneficiary = await this.db.userBeneficiaries.create({
          data: {
            userId: id,
            beneficiaryDetailId: recipient.id,
            lastUsedAt: new Date().toISOString(),
            NoOfTransactions: 0,
          },
        })
      }

      beneficiary = await this.db.userBeneficiaries.update({
        data: {
          lastUsedAt: new Date().toISOString(),
          NoOfTransactions: beneficiary.NoOfTransactions + 1,
          deletedAt: null,
        },
        where: {
          userId: id,
          id: beneficiary.id,
        },
      })
      /**
       * !RESTORE WHEN I RECEIVE CREDENTIALS
       */
      // const txn = await this.db.transaction.create({
      //   data: {
      //     userId: id,
      //     transactionId: transferId.toString(),
      //     baseTransactionAmount: amount_.toFixed(2),
      //     transactionAmount: (amount_ / exchangeRate.value).toFixed(2),
      //     exchangeRate,
      //     transactionStatusId: transactionStatus.id,
      //     transactionTypeId: transactionType.id,
      //     paystackTransferCode: transfer_code,
      //     beneficiaryId: beneficiary.id,
      //     paystackReference: reference,
      //   },
      //   include: {
      //     transactionStatus: true,
      //     transactionType: true,
      //     beneficiary: {
      //       include: {
      //         BeneficiaryDetails: true,
      //       },
      //     },
      //   },
      // })

      /**
       * !END OF RESTORE WHEN I RECEIVE CREDENTIALS
       */

      /**
       *
       * ! DUMMY START
       */
      function generateRandomNumbers(digits: number): number {
        if (digits < 3 || digits > 10) {
          throw new Error('Digits must be between 3 and 10')
        }

        // Calculate min and max values for the given number of digits
        const min = Math.pow(10, digits - 1)
        const max = Math.pow(10, digits) - 1

        // Generate cryptographically secure random number
        const randomBuffer = new Uint32Array(1)
        getRandomValues(randomBuffer)

        // Scale and floor the random value
        return (
          min +
          Math.floor((randomBuffer[0] / (0xffffffff + 1)) * (max - min + 1))
        )
      }

      const amount_ = amount
      const transactionType = await this.getTransactionType('transfer')
      const transactionStatus = await this.getTransactionStatus('succeeded')
      const txn = await this.db.transaction.create({
        data: {
          userId: id,
          transactionId: generateRandomNumbers(10).toString(),
          baseTransactionAmount: amount_.toFixed(2),
          transactionAmount: (amount_ / exchangeRate.value).toFixed(2),
          exchangeRate,
          transactionStatusId: transactionStatus.id,
          transactionTypeId: transactionType.id,
          paystackTransactionStatus: 'success',
          paystackTransferCode: randomUUID(),
          beneficiaryId: beneficiary.id,
        },
        include: {
          transactionStatus: true,
          transactionType: true,
          beneficiary: {
            include: {
              BeneficiaryDetails: true,
            },
          },
        },
      })
      /**
       * ! Wallet value in dollar
       */
      const newBalance = user_.Wallet.balance.minus(
        amount_ / exchangeRate.value,
      )

      /**
       * ! Wallet value in naira
       */
      const newBaseBalance = user_.Wallet.baseBalance.minus(amount_)

      const walletBalance = await this.db.walletBalance.create({
        data: {
          walletId: user_.Wallet.id,
          transactionId: txn.id,
          baseBalance: newBaseBalance,
          balance: newBalance,
        },
      })

      await this.db.userWallet.update({
        data: {
          balance: newBalance,
          baseBalance: newBaseBalance,
          balanceHistory: { connect: walletBalance },
        },
        where: {
          id: user_.Wallet.id,
        },
      })
      /**
       *
       * ! DUMMY END
       */

      return this.createResponse(
        201,
        'success',
        'Transfer initialized successfully',

        {
          ...txn,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async retrieveTransfer(
    transferId: string,
    user: UserPayload,
  ): Promise<InternalResponseReturns<any>> {
    const { id } = user

    try {
      const user_ = await this.db.user.findUnique({
        where: { id },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }

      const txn = await this.db.transaction.findUnique({
        where: {
          id: +transferId,
          userId: id,
        },
        include: {
          transactionStatus: true,
          transactionType: true,
          beneficiary: {
            include: {
              BeneficiaryDetails: true,
            },
          },
        },
      })

      if (!txn) {
        throw new BadRequestError('Transaction not found')
      }

      return this.createResponse(
        200,
        'success',
        'Payment returned successfully',

        {
          ...txn,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async retrieveBeneficiaries(
    pageDetails: PaginatedBeneficiaryQueryDto,
    user: UserPayload,
  ): Promise<
    InternalResponseReturns<
      ReturnType<typeof paginationData<UserBeneficiaries[]>>
    >
  > {
    const { id } = user

    try {
      const user_ = await this.db.user.findUnique({
        where: { id },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }
      const {
        page,
        hitsPerPage,
        searchString,

        isFavorite,
      } = pageDetails

      const { skip, take } = getPagination({
        page: page ? +page : 1,
        size: hitsPerPage ? +hitsPerPage : 1,
      })

      const txns = await this.db.userBeneficiaries.findMany({
        where: {
          userId: id,
          BeneficiaryDetails: {
            accountName: {
              contains:
                searchString && isNaN(+searchString) ? searchString : undefined,
            },
            accountNumber: {
              contains:
                searchString && !isNaN(+searchString)
                  ? searchString
                  : undefined,
            },
          },
          isFavorite: isFavorite ? true : undefined,
          deletedAt: null,
        },
        take: hitsPerPage && page ? take : undefined,
        skip: hitsPerPage && page ? skip : undefined,
        include: {
          BeneficiaryDetails: true,
        },
      })

      const txnsCount = await this.db.userBeneficiaries.count({
        where: {
          userId: id,
          deletedAt: null,
          BeneficiaryDetails: {
            accountName: {
              contains: searchString ? searchString : undefined,
            },
            accountNumber: {
              contains: searchString ? searchString : undefined,
            },
            bankName: {
              contains: searchString ? searchString : undefined,
            },
          },
        },
      })

      return this.createResponse(
        200,
        'success',
        'Beneficiaries returned successfully',

        {
          ...paginationData({
            data: txns,
            page: page ? +page : 1,
            size: hitsPerPage ? +hitsPerPage : 1,
            total: txnsCount,
            paginate: page && hitsPerPage ? true : false,
          }),
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async toggleBeneficiaryFavorite(
    body: ToggleFavoriteBeneficiariesBodyDTO & {
      beneficiaryId: string
    },
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user

    try {
      const user_ = await this.db.user.findUnique({
        where: { id },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }
      const { isFavorite, beneficiaryId } = body
      await this.db.userBeneficiaries.update({
        where: {
          userId: id,
          id: +beneficiaryId,
        },
        data: {
          isFavorite,
          deletedAt: null,
        },
      })

      return this.createResponse(
        200,
        'success',
        'Beneficiary updated successfully',
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async detachBeneficiaryFromUser(
    beneficiaryId: string,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user

    try {
      const user_ = await this.db.user.findUnique({
        where: { id },
      })

      if (!user_) {
        throw new BadRequestError('User not found')
      }
      await this.db.userBeneficiaries.update({
        where: {
          userId: id,
          id: +beneficiaryId,
        },
        data: {
          deletedAt: new Date().toISOString(),
        },
      })

      return this.createResponse(
        200,
        'success',
        'Beneficiary deleted successfully',
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
}
