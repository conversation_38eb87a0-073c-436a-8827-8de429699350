import { inject, injectable } from 'tsyringe'
import {
  Controller,
  Route,
  Post,
  SuccessResponse,
  Body,
  Tags,
  Security,
  Request,
  Get,
  Path,
  Query,
  Middlewares,
} from 'tsoa'
import { KYCService } from './kyc.service'
import { AuthenticatedRequest } from '../../types/req.types'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import {
  GetKYCSessionStatusSchema,
  StartPassportVerificationSessionSchema,
  StartPassportVerificationSessionType,
  StartVisaVerificationSessionSchema,
  StartVisaVerificationSessionType,
  SupportedVerifDocumentSchema,
  SupportedVerifDocumentType,
} from '../../types/dtos.types'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import express from 'express'

@Tags('KYC Controller')
@Route('kyc')
@injectable()
@Middlewares(express.json())
export class KY<PERSON>ontroller extends Controller {
  constructor(@inject('kycService') private kycService: KYCService) {
    super()
  }

  @Get('user')
  @Security('jwt')
  @SuccessResponse('200', 'Ok')
  public async getUserKYC(@Request() request: AuthenticatedRequest) {
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.kycService.getUserKYC(user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('session/passport')
  @Security('jwt')
  @SuccessResponse('201', 'Created')
  public async createPassportVerificationSession(
    @Request() request: AuthenticatedRequest,
    @Body() body: StartPassportVerificationSessionType,
  ) {
    const check =
      await StartPassportVerificationSessionSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<StartPassportVerificationSessionType>(
        check.error,
        'body',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.kycService.createPassportKYCSession(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Post('session/visa')
  @Security('jwt')
  @SuccessResponse('201', 'Created')
  public async createVisaVerificationSession(
    @Request() request: AuthenticatedRequest,
    @Body() body: StartVisaVerificationSessionType,
  ) {
    const check = await StartVisaVerificationSessionSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<StartVisaVerificationSessionType>(
        check.error,
        'body',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.kycService.createVisaKYCSession(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Get('{sessionId}')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async getKYCStatus(
    @Request() request: AuthenticatedRequest,
    @Path() sessionId: string,
    @Query() idType: SupportedVerifDocumentType,
  ) {
    const check = await GetKYCSessionStatusSchema.safeParseAsync(sessionId)
    if (!check.success) {
      throw new ZodErrorToValidateError<string>(check.error, 'path')
    }

    const checkQuery = await SupportedVerifDocumentSchema.safeParseAsync(idType)
    if (!checkQuery.success) {
      throw new ZodErrorToValidateError<SupportedVerifDocumentType>(
        checkQuery.error,
        'query',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.kycService.getKYCStatus(sessionId, idType, user)
    this.setStatus(response.statusCode)
    return response
  }
}
