import { singleton } from 'tsyringe'
import { prisma } from '../../config/prisma.config'
import { PrismaClient } from '@prisma/client'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import { AddCardBodyDto, RetrieveCardDto } from '../../types/dtos.types'
import { UserPayload } from '../../types/req.types'
import { BadRequestError } from '../../errors/BadRequestError'
import { PaymentsManager } from '../../config/stripe.config'
import { logger } from '../../helpers/logger'
import { safeString } from '../../utils/utils'
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
} from '@prisma/client/runtime/library'
import { HTTPError } from '../../errors/HTTPError'
import Stripe from 'stripe'

@singleton()
export class CardsService {
  private createResponse: typeof internalResponse
  private db: PrismaClient
  private paymentsManager: PaymentsManager

  constructor() {
    this.createResponse = internalResponse
    this.db = prisma
    this.paymentsManager = new PaymentsManager()
  }

  public async addCard(
    body: AddCardBodyDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { id } = user
    const { token } = body
    try {
      let user_ = await this.db.user.findUnique({ where: { id } })
      let stripeCustomerId = user_?.stripeCustomerId
      if (!user_) {
        throw new BadRequestError('User not found')
      }
      if (!user_.stripeCustomerId) {
        const { data, error } = await this.paymentsManager.createCustomer(
          user_.email,
        )

        if (error || !data) {
          logger(module).error(JSON.stringify(error, null, 2))
          throw new BadRequestError('Unable to add card, try again later')
        }
        logger(module).info(JSON.stringify(data, null, 2))
        user_ = await this.db.user.update({
          where: { id },
          data: { stripeCustomerId: data.id },
        })
        stripeCustomerId = data.id
      }

      const { data, error } = await this.paymentsManager.addCard(
        token,
        safeString(stripeCustomerId),
      )

      if (error || !data) {
        logger(module).error(
          `Stripe card addition failed with error: 
          ${JSON.stringify(error, null, 2)}`,
        )
        throw new BadRequestError(
          'Unable to add card to customer, try again later',
        )
      }

      logger(module).info(
        `Stripe card added with data: 
        ${JSON.stringify(data, null, 2)}`,
      )
      return this.createResponse(201, 'success', 'Card added successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async getAllCardsForUser(user: UserPayload): Promise<
    InternalResponseReturns<{
      cards: Stripe.CustomerSource[] | null
    }>
  > {
    const { id } = user

    try {
      let user_ = await this.db.user.findUnique({ where: { id } })
      if (!user_) {
        throw new BadRequestError(' User not found')
      }

      if (!user_.stripeCustomerId) {
        const { data, error } = await this.paymentsManager.createCustomer(
          user_.email,
        )

        if (error || !data) {
          logger(module).error(JSON.stringify(error, null, 2))
          throw new BadRequestError('Unable to add card, try again later')
        }
        logger(module).info(JSON.stringify(data, null, 2))
        user_ = await this.db.user.update({
          where: { id },
          data: { stripeCustomerId: data.id },
        })
      }
      const cards = await this.paymentsManager.listCards(
        safeString(user_.stripeCustomerId),
      )

      if (cards.error || !cards.data) {
        logger(module).error(JSON.stringify(cards.error, null, 2))
        throw new BadRequestError('Unable to fetch resource')
      }

      return this.createResponse(200, 'success', 'Cards fetched successfully', {
        cards: cards.data.data,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async getCard(
    user: UserPayload,
    pathParams: RetrieveCardDto,
  ): Promise<
    InternalResponseReturns<{
      card: Stripe.CustomerSource | null
    }>
  > {
    const { id } = user
    const { cardId } = pathParams

    try {
      let user_ = await this.db.user.findUnique({ where: { id } })
      if (!user_) {
        throw new BadRequestError(' User not found')
      }

      if (!user_.stripeCustomerId) {
        const { data, error } = await this.paymentsManager.createCustomer(
          user_.email,
        )

        if (error || !data) {
          logger(module).error(JSON.stringify(error, null, 2))

          throw new BadRequestError('Unable to add card, try again later')
        }
        logger(module).info(JSON.stringify(data, null, 2))

        user_ = await this.db.user.update({
          where: { id },
          data: { stripeCustomerId: data.id },
        })
      }
      const card = await this.paymentsManager.getCard({
        stripeCustomerId: safeString(user_.stripeCustomerId),
        cardId,
      })

      if (card.error || !card.data) {
        logger(module).error(JSON.stringify(card.error, null, 2))

        throw new BadRequestError('Unable to fetch resource')
      }

      return this.createResponse(200, 'success', 'Cards fetched successfully', {
        card: card.data,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
}
