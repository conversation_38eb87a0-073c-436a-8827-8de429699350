import { singleton } from 'tsyringe'
import { prisma } from '../../config/prisma.config'
import { HttpStatusCodeLiteral } from 'tsoa'
import { Prisma, PrismaClient } from '@prisma/client'
import {
  VerifService,
  VerifSessionDecisionResponse,
} from '../../config/verif.config'
import { safeString } from '../../utils/utils'
import { logger } from '../../helpers/logger'
import { PaymentsManager } from '../../config/stripe.config'
import Stripe from 'stripe'
import { PaystackTransferEvent, transactionType } from '../../types/req.types'
import { PaystackService } from '../../config/paystack.config'
import { IMTOService } from '../../config/imto.config'

@singleton()
export class WebhookService {
  private db: PrismaClient
  private kycProvider: VerifService
  private paymentsManager: PaymentsManager
  private paystackService: PaystackService
  private IMTOService: IMTOService

  constructor() {
    this.db = prisma
    this.kycProvider = new VerifService()
    this.paymentsManager = new PaymentsManager()
    this.paystackService = new PaystackService()
    this.IMTOService = new IMTOService()
  }

  private async handlePassportKYCDecision(body: VerifSessionDecisionResponse) {
    const {
      endUserId,
      id,
      status,
      document,
      person,
      riskScore,
      riskLabels,
      decisionTime,
      acceptanceTime,
      additionalVerifiedData,
      reason,
    } = body.verification

    const { ip } = body.technicalData

    try {
      const attemptExists = await this.db.passportKycAttempts.findFirst({
        where: { sessionId: id, userId: safeString(endUserId) },
        include: { verifiedDetails: true },
      })

      if (!attemptExists) {
        logger(module).info(
          `ID KYC attempt not found in db, ending webhook cycle. Payload: ${JSON.stringify(body, null, 2)}`,
        )
        return 200
      }

      if (attemptExists?.verifiedDetails) {
        logger(module).info(
          `KYC already approved, ending webhook cycle. Payload: ${JSON.stringify(body, null, 2)}`,
        )
        return 200
      }

      if (status === 'approved') {
        logger(module).info(
          `KYC approved, updating records with payload: ${JSON.stringify(body, null, 2)}`,
        )
        await this.db.passportKycAttempts.update({
          where: { id: attemptExists.id },
          data: {
            status: status,
            verifiedDetails: {
              create: {
                userId: safeString(endUserId),
                gender: person.gender,
                idNumber: document.number,
                lastName: person.lastName,
                firstName: person.firstName,
                nationality: person.nationality,
                dateOfBirth: person.dateOfBirth
                  ? new Date(person.dateOfBirth)
                  : null,
                placeOfBirth: person.placeOfBirth,
                occupation: person.occupation,
                extraNames: person.extraNames,
                employer: person.employer,
                riskScore: riskScore
                  ? new Prisma.Decimal(riskScore?.score)
                  : null,
                riskLabels: riskLabels,
                document: document,
                ip,
                decisionTime: decisionTime ? new Date(decisionTime) : null,
                acceptanceTime: acceptanceTime
                  ? new Date(acceptanceTime)
                  : null,
                additionalVerifiedData,
              },
            },
          },
        })
      } else {
        logger(module).info(
          `KYC ${status}, updating records with payload: ${JSON.stringify(body, null, 2)}`,
        )
        await this.db.passportKycAttempts.update({
          where: { id: attemptExists.id },
          data: {
            status: status,
            reason: reason,
          },
        })
      }

      return 200
    } catch (error) {
      logger(module).info(
        `Failed to update kYC status in db with error: ${JSON.stringify(error, null, 2)}`,
      )
      return 400
    }
  }
  private async handleVisaKYCDecision(body: VerifSessionDecisionResponse) {
    const {
      endUserId,
      id,
      status,
      document,
      person,
      riskScore,
      riskLabels,
      decisionTime,
      acceptanceTime,
      additionalVerifiedData,
      reason,
    } = body.verification

    const { ip } = body.technicalData

    try {
      const attemptExists = await this.db.visaKycAttempts.findFirst({
        where: { sessionId: id, userId: safeString(endUserId) },
        include: { verifiedDetails: true },
      })

      if (!attemptExists) {
        logger(module).info(
          `ID KYC attempt not found in db, ending webhook cycle. Payload: ${JSON.stringify(body, null, 2)}`,
        )
        return 200
      }

      if (attemptExists?.verifiedDetails) {
        logger(module).info(
          `KYC already approved, ending webhook cycle. Payload: ${JSON.stringify(body, null, 2)}`,
        )
        return 200
      }
      if (status === 'approved') {
        logger(module).info(
          `KYC approved, updating records with payload: ${JSON.stringify(body, null, 2)}`,
        )
        const attempt = await this.db.visaKycAttempts.update({
          where: { id: attemptExists.id },
          data: {
            status: status,
          },
        })

        await this.db.passportKycDetails.create({
          data: {
            userId: safeString(endUserId),
            gender: person.gender,
            idNumber: document.number,
            lastName: person.lastName,
            firstName: person.firstName,
            nationality: person.nationality,
            dateOfBirth: person.dateOfBirth
              ? new Date(person.dateOfBirth)
              : null,
            placeOfBirth: person.placeOfBirth,
            occupation: person.occupation,
            extraNames: person.extraNames,
            employer: person.employer,
            riskScore: riskScore ? new Prisma.Decimal(riskScore?.score) : null,
            riskLabels: riskLabels,
            document: document,
            ip,
            decisionTime: decisionTime ? new Date(decisionTime) : null,
            acceptanceTime: acceptanceTime ? new Date(acceptanceTime) : null,
            additionalVerifiedData,
            attemptId: attempt.id,
          },
        })
      } else {
        logger(module).info(
          `KYC ${status}, updating records with payload: ${JSON.stringify(body, null, 2)}`,
        )
        await this.db.visaKycAttempts.update({
          where: { id: attemptExists.id },
          data: {
            status: status,
            reason: reason,
          },
        })
      }

      return 200
    } catch (error) {
      logger(module).info(
        `Failed to update kYC status in db with error: ${JSON.stringify(error, null, 2)}`,
      )
      return 400
    }
  }

  async verifEvents(
    body: VerifSessionDecisionResponse,
    signature: string,
  ): Promise<HttpStatusCodeLiteral> {
    try {
      const isValidPayload = await this.kycProvider.validateHMACHeader({
        payload: body,
        signature,
      })

      if (!isValidPayload) {
        logger(module).info(
          `Invalid Verif Decision Payload. Payload: ${JSON.stringify(body, null, 2)}. HMAC Signature: ${signature}`,
        )
        return 401
      }
      logger(module).info(
        `Verif webhook event received with payload: ${JSON.stringify(body, null, 2)}`,
      )
      return 200
    } catch {
      return 400
    }
  }
  async verifDecisions(
    body: VerifSessionDecisionResponse,
    signature: string,
  ): Promise<HttpStatusCodeLiteral> {
    try {
      const isValidPayload = await this.kycProvider.validateHMACHeader({
        payload: body,
        signature,
      })

      if (!isValidPayload) {
        logger(module).info(
          `Invalid Verif Decision Payload. Payload: ${JSON.stringify(body, null, 2)}. HMAC Signature: ${signature}`,
        )
        return 401
      }

      const { status: responseStatus, verification } = body
      if (responseStatus !== 'success') {
        logger(module).info(
          `Verif webhook request returned a non success response. Payload: ${JSON.stringify(body, null, 2)}`,
        )
        return 400
      }
      const { document } = verification

      switch (document.type) {
        case 'PASSPORT':
          return await this.handlePassportKYCDecision(body)
        case 'VISA':
          return await this.handleVisaKYCDecision(body)
        default:
          logger(module).info(
            `Invalid document type returned by webhook, ending webhook cycle. Payload: ${JSON.stringify(body, null, 2)}`,
          )
          return 200
      }
    } catch {
      return 400
    }
  }

  private async getStripeTransactionStatus(status: Stripe.Charge.Status) {
    const pendingStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'pending' },
      create: {
        statusName: 'pending',
      },
      update: {},
    })
    const successStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'success' },
      create: { statusName: 'success' },
      update: {},
    })
    const failureStatus = await this.db.transactionStatus.upsert({
      where: { statusName: 'failed' },
      create: {
        statusName: 'failed',
      },
      update: {},
    })

    if (status === 'succeeded') {
      return successStatus
    }
    if (status === 'failed') {
      return failureStatus
    }

    return pendingStatus
  }
  private async getTransactionType(type: transactionType) {
    return await this.db.transactionType.upsert({
      where: { typeName: type },
      create: {
        typeName: type,
      },
      update: {},
    })
  }

  async handlePaymentIntentResponse(
    body: any,
    signature: string,
  ): Promise<HttpStatusCodeLiteral> {
    try {
      const event = this.paymentsManager.constructWebhookEvent({
        signature,
        body,
      })

      logger(module).info(
        `Stripe webhook event: ${JSON.stringify(event, null, 2)}`,
      )

      const exchangeRate = this.IMTOService.getExchangeRate()

      switch (event.type) {
        case 'payment_intent.amount_capturable_updated': {
          logger(module).info(
            `Payment intent amount capturable updated event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )

          const { client_secret, id, status, metadata, amount } =
            event.data.object

          const transactionType = await this.getTransactionType('credit')

          const transactionStatus =
            await this.getStripeTransactionStatus('pending')

          await this.db.transaction.upsert({
            where: {
              transactionId: id,
              stripeClientSecret: client_secret,
              userId: metadata.userId,
            },
            update: { stripeTransactionStatus: status },
            create: {
              transactionId: id,
              stripeClientSecret: client_secret,
              stripeTransactionStatus: status,
              transactionAmount: amount.toFixed(2),
              baseTransactionAmount: (amount * exchangeRate.value).toFixed(2),
              transactionTypeId: transactionType.id,
              transactionStatusId: transactionStatus.id,
              exchangeRate,
              userId: metadata.userId,
            },
          })
          break
        }
        case 'payment_intent.canceled': {
          logger(module).info(
            `Payment intent cancelled event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )

          const { client_secret, id, status, metadata, amount } =
            event.data.object

          const transactionType = await this.getTransactionType('credit')

          const transactionStatus =
            await this.getStripeTransactionStatus('failed')

          const txn = await this.db.transaction.findFirst({
            where: {
              transactionId: id,
              stripeClientSecret: client_secret,
              userId: metadata.userId,
            },
            include: {
              transactionStatus: true,
            },
          })

          if (txn?.transactionStatus.statusName === 'pending') {
            await this.db.transaction.upsert({
              where: {
                transactionId: id,
                stripeClientSecret: client_secret,
                userId: metadata.userId,
              },
              update: {
                stripeTransactionStatus: status,
                transactionStatusId: transactionStatus.id,
              },
              create: {
                transactionId: id,
                stripeClientSecret: client_secret,
                stripeTransactionStatus: status,
                transactionAmount: amount.toFixed(2),
                baseTransactionAmount: (amount * exchangeRate.value).toFixed(2),
                transactionTypeId: transactionType.id,
                transactionStatusId: transactionStatus.id,
                exchangeRate,
                userId: metadata.userId,
              },
            })
          }

          break
        }
        case 'payment_intent.created': {
          logger(module).info(
            `Payment intent created event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )
          break
        }

        case 'payment_intent.payment_failed': {
          logger(module).info(
            `Payment intent payment failed event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )
          const { client_secret, id, status, metadata, amount } =
            event.data.object

          const transactionType = await this.getTransactionType('credit')

          const transactionStatus =
            await this.getStripeTransactionStatus('failed')
          const txn = await this.db.transaction.findFirst({
            where: {
              transactionId: id,
              stripeClientSecret: client_secret,
              userId: metadata.userId,
            },
            include: {
              transactionStatus: true,
            },
          })

          if (txn?.transactionStatus.statusName === 'pending') {
            await this.db.transaction.upsert({
              where: {
                transactionId: id,
                stripeClientSecret: client_secret,
                userId: metadata.userId,
              },
              update: {
                stripeTransactionStatus: status,
                transactionStatusId: transactionStatus.id,
              },
              create: {
                transactionId: id,
                stripeClientSecret: client_secret,
                stripeTransactionStatus: status,
                transactionAmount: amount.toFixed(2),
                baseTransactionAmount: (amount * exchangeRate.value).toFixed(2),
                transactionTypeId: transactionType.id,
                transactionStatusId: transactionStatus.id,
                exchangeRate,
                userId: metadata.userId,
              },
            })
          }
          break
        }
        case 'payment_intent.processing': {
          logger(module).info(
            `Payment intent payment processing event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )
          const { client_secret, id, status, metadata, amount } =
            event.data.object

          const transactionType = await this.getTransactionType('credit')

          const transactionStatus =
            await this.getStripeTransactionStatus('pending')

          const txn = await this.db.transaction.findFirst({
            where: {
              transactionId: id,
              stripeClientSecret: client_secret,
              userId: metadata.userId,
            },
            include: {
              transactionStatus: true,
            },
          })

          if (txn?.transactionStatus.statusName === 'pending') {
            await this.db.transaction.upsert({
              where: {
                transactionId: id,
                stripeClientSecret: client_secret,
                userId: metadata.userId,
              },
              update: {
                stripeTransactionStatus: status,
              },
              create: {
                transactionId: id,
                stripeClientSecret: client_secret,
                stripeTransactionStatus: status,
                transactionAmount: amount.toFixed(2),
                baseTransactionAmount: (amount * exchangeRate.value).toFixed(2),
                transactionTypeId: transactionType.id,
                transactionStatusId: transactionStatus.id,
                exchangeRate,
                userId: metadata.userId,
              },
            })
          }
          break
        }
        case 'payment_intent.requires_action': {
          logger(module).info(
            `Payment intent payment requires action event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )
          const { client_secret, id, status, metadata, amount } =
            event.data.object

          const transactionType = await this.getTransactionType('credit')

          const transactionStatus =
            await this.getStripeTransactionStatus('pending')

          const txn = await this.db.transaction.findFirst({
            where: {
              transactionId: id,
              stripeClientSecret: client_secret,
              userId: metadata.userId,
            },
            include: {
              transactionStatus: true,
            },
          })

          if (txn?.transactionStatus.statusName === 'pending') {
            await this.db.transaction.upsert({
              where: {
                transactionId: id,
                stripeClientSecret: client_secret,
                userId: metadata.userId,
              },
              update: {
                stripeTransactionStatus: status,
              },
              create: {
                transactionId: id,
                stripeClientSecret: client_secret,
                stripeTransactionStatus: status,
                transactionAmount: amount.toFixed(2),
                baseTransactionAmount: (amount * exchangeRate.value).toFixed(2),
                transactionTypeId: transactionType.id,
                transactionStatusId: transactionStatus.id,
                exchangeRate,
                userId: metadata.userId,
              },
            })
          }
          break
        }
        case 'payment_intent.succeeded': {
          logger(module).info(
            `Payment intent payment succeeded event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )
          const { client_secret, id, status, metadata, amount } =
            event.data.object

          const transactionType = await this.getTransactionType('credit')

          const transactionStatus =
            await this.getStripeTransactionStatus('succeeded')
          const userWallet = await this.db.userWallet.upsert({
            create: { userId: metadata.userId },
            update: {},
            where: { userId: metadata.userId },
          })

          const tn = await this.db.transaction.findFirst({
            where: {
              transactionId: id,
              stripeClientSecret: client_secret,
              userId: metadata.userId,
            },
            include: {
              transactionStatus: true,
            },
          })
          if (tn?.transactionStatus.statusName === 'pending') {
            const txn = await this.db.transaction.upsert({
              where: {
                transactionId: id,
                stripeClientSecret: client_secret,
                userId: metadata.userId,
              },
              update: {
                stripeTransactionStatus: status,
                transactionStatusId: transactionStatus.id,
              },
              create: {
                transactionId: id,
                stripeClientSecret: client_secret,
                stripeTransactionStatus: status,
                transactionAmount: amount.toFixed(2),
                baseTransactionAmount: (amount * exchangeRate.value).toFixed(2),
                transactionTypeId: transactionType.id,
                transactionStatusId: transactionStatus.id,
                exchangeRate,
                userId: metadata.userId,
              },
            })

            /**
             * ! Wallet value in dollar
             */
            const newBalance = userWallet.balance.plus(amount)

            /**
             * ! Wallet value in naira
             */
            const newBaseBalance = userWallet.baseBalance.plus(
              amount * exchangeRate.value,
            )

            const walletBalance = await this.db.walletBalance.create({
              data: {
                walletId: userWallet.id,
                transactionId: txn.id,
                baseBalance: newBaseBalance,
                balance: newBalance,
              },
            })
            await this.db.userWallet.update({
              data: {
                balance: newBalance,
                baseBalance: newBaseBalance,
                balanceHistory: { connect: walletBalance },
              },
              where: {
                id: userWallet.id,
              },
            })
          }
          break
        }
        default:
          logger(module).error(
            `Unhandled event type ${event.type} event received with data: ${JSON.stringify(event.data.object, null, 2)}`,
          )
      }
      return 200
    } catch (error) {
      logger(module).error(
        `Stripe webhook error: ${JSON.stringify(error, null, 2)}`,
      )
      return 400
    }
  }

  async handlePaystackTransferResponse(
    body: PaystackTransferEvent,
    signature: string,
  ): Promise<HttpStatusCodeLiteral> {
    try {
      const isValidRequest = this.paystackService.validateHMACHeader({
        payload: body,
        signature,
      })

      if (!isValidRequest) {
        return 401
      }
      logger(module).info(
        `Paystack webhook event: ${JSON.stringify(body, null, 2)}`,
      )

      const exchangeRate = this.IMTOService.getExchangeRate()

      switch (body.event) {
        case 'transfer.failed': {
          logger(module).info(
            `transfer.failed event received with data: ${JSON.stringify(body.data, null, 2)}`,
          )

          const { id, transfer_code, status, reference } = body.data

          const txn = await this.db.transaction.findFirst({
            where: {
              transactionId: id.toString(),
              paystackTransferCode: transfer_code,
              paystackReference: reference,
            },
            include: {
              transactionStatus: true,
            },
          })
          if (!txn) {
            return 400
          }
          if (txn?.transactionStatus.statusName === 'pending') {
            const transactionStatus =
              await this.getStripeTransactionStatus('failed')

            await this.db.transaction.update({
              where: {
                transactionId: id.toString(),
                paystackTransferCode: transfer_code,
                paystackReference: reference,
                paystackTransactionStatus: status,
              },
              data: {
                transactionStatusId: transactionStatus.id,
                paystackTransactionStatus: status,
              },
            })
          }
          break
        }
        case 'transfer.reversed': {
          logger(module).info(
            `transfer.reversed event received with data: ${JSON.stringify(body.data, null, 2)}`,
          )

          const { id, transfer_code, status, reference } = body.data

          const transactionStatus =
            await this.getStripeTransactionStatus('failed')

          const txn = await this.db.transaction.findFirst({
            where: {
              transactionId: id.toString(),
              paystackTransferCode: transfer_code,
              paystackReference: reference,
            },
            include: {
              transactionStatus: true,
            },
          })
          if (!txn) {
            return 400
          }
          if (txn?.transactionStatus.statusName === 'pending') {
            await this.db.transaction.update({
              where: {
                transactionId: id.toString(),
                paystackTransferCode: transfer_code,
                paystackReference: reference,
                paystackTransactionStatus: status,
              },
              data: {
                transactionStatusId: transactionStatus.id,
                paystackTransactionStatus: status,
              },
            })
          }

          break
        }
        case 'transfer.success': {
          logger(module).info(
            `transfer.success event received with data: ${JSON.stringify(body.data, null, 2)}`,
          )

          const { id, transfer_code, status, amount, reference } = body.data

          const transactionStatus =
            await this.getStripeTransactionStatus('succeeded')

          const txn = await this.db.transaction.findFirst({
            where: {
              transactionId: id.toString(),
              paystackTransferCode: transfer_code,
              paystackReference: reference,
            },
            include: {
              transactionStatus: true,
              user: true,
            },
          })
          if (!txn) {
            return 400
          }

          if (
            txn.transactionStatus.statusName === 'success' ||
            txn.transactionStatus.statusName === 'failed'
          ) {
            return 200
          }
          if (txn.transactionStatus.statusName === 'pending') {
            await this.db.transaction.update({
              where: {
                transactionId: id.toString(),
                paystackTransferCode: transfer_code,
                paystackReference: reference,
              },
              data: {
                transactionStatusId: transactionStatus.id,
                paystackTransactionStatus: status,
              },
            })

            const userWallet = await this.db.userWallet.upsert({
              create: { userId: txn.userId },
              update: {},
              where: { userId: txn.userId },
            })

            /**
             * ! Wallet value in dollar
             */
            const newBalance = userWallet.balance.minus(
              amount / exchangeRate.value,
            )

            /**
             * ! Wallet value in naira
             */
            const newBaseBalance = userWallet.baseBalance.minus(amount)

            const walletBalance = await this.db.walletBalance.create({
              data: {
                walletId: userWallet.id,
                transactionId: txn.id,
                baseBalance: newBaseBalance,
                balance: newBalance,
              },
            })

            await this.db.userWallet.update({
              data: {
                balance: newBalance,
                baseBalance: newBaseBalance,
                balanceHistory: { connect: walletBalance },
              },
              where: {
                id: userWallet.id,
              },
            })
          }

          break
        }
        default:
          logger(module).info(
            `unhandled event type received with data: ${JSON.stringify(body.data, null, 2)}`,
          )
      }
      return 200
    } catch (error) {
      logger(module).error(
        `Paystack webhook error: ${JSON.stringify(error, null, 2)}`,
      )
      return 400
    }
  }
}
