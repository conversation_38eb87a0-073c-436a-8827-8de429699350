import { Ioc<PERSON>ontainer, IocContainerFactory } from '@tsoa/runtime'
import { AuthService } from '../api/auth/auth.service'
import { container } from 'tsyringe'
import { UserService } from '../api/user/user.service'
import { PaymentService } from '../api/payments/payment.service'
import { CardsService } from '../api/cards/cards.service'
import { BanksService } from '../api/banks/banks.service'
import { KYCService } from '../api/kyc/kyc.service'
import { OnboardingService } from '../api/onboarding/onboarding.service'
import { WebhookService } from '../api/webhooks/webhooks.service'
import { TransfersService } from '../api/transfer/transfer.service'

container.register('authService', { useClass: AuthService })
container.register('userService', { useClass: UserService })
container.register('paymentService', { useClass: PaymentService })
container.register('cardsService', { useClass: CardsService })
container.register('banksService', { useClass: BanksService })
container.register('onboardingService', { useClass: OnboardingService })
container.register('kycService', { useClass: KYCService })
container.register('webhookService', { useClass: WebhookService })
container.register('transfersService', { useClass: TransfersService })
const iocContainer: IocContainerFactory = function (): IocContainer {
  return {
    get: <T>(controller: { prototype: T }): T => {
      return container.resolve<T>(controller as never)
    },
  }
}
export { iocContainer }
