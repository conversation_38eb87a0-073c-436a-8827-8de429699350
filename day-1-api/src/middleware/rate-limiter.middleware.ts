import rateLimit, { Options } from 'express-rate-limit'

export const createRateLimiter = (
  props: Pick<Options, 'windowMs' | 'limit'> & { skipFailedRequests?: boolean },
) => {
  const { windowMs, limit, skipFailedRequests } = props
  return rateLimit({
    windowMs,
    limit,
    statusCode: 429,
    message: 'Too many requests, please try again later.',
    headers: true,
    skipFailedRequests,
    handler: (_, res, __, options) => {
      return res.status(options.statusCode).json({
        status: 'error',
        message: options.message,
        retryAfter: `${Math.ceil(options.windowMs / 1000)}s`,
      })
    },
  })
}
