import {
  Response as ExResponse,
  Request as ExRequest,
  NextFunction,
} from 'express'
import { FieldErrors, ValidateError } from 'tsoa'
import {
  createHTTPResponse,
  hideDevelopmentMessage,
} from '../helpers/response-helpers'
import { safeString } from '../utils/utils'
import { logger } from '../helpers/logger'
import { ZodError } from 'zod'
import { HTTPError } from '../errors/HTTPError'

/**
 * TODO: add custom config to deal with prisma errors
 */
export function errorHandler(
  err: unknown,
  req: ExRequest,
  res: ExResponse,
  next: NextFunction,
): ExResponse | void {
  if (err instanceof ValidateError) {
    logger(module).error(
      `Caught Validation Error for ${req.path}: ${JSON.stringify(err.fields, null, 2)}`,
    )
    return createHTTPResponse(res, {
      status: 'error',
      statusCode: 422,
      message: 'Validation Error',
      data: err.fields,
    })
  }
  if (err instanceof HTTPError) {
    logger(module).error(
      `${req.path}
       http error: 
      ${JSON.stringify(err, null, 2)}`,
    )
    return createHTTPResponse(res, {
      status: 'error',
      message: err.message,
      statusCode: err.statusCode,
    })
  }
  if (err instanceof Error) {
    logger(module).error(JSON.stringify(err, null, 2))
    return createHTTPResponse(res, {
      message: hideDevelopmentMessage(
        safeString(err?.message, 'Something went wrong'),
      ),
      status: 'error',
      statusCode: 500,
    })
  }

  next()
}

const ZodErrorToValidateError_ = <T>(
  error: ZodError<T>,
  fieldKey: string = 'body',
): ValidateError => {
  const fields: FieldErrors = {}

  error.errors.forEach((err) => {
    if (fields[fieldKey + '.' + err.path[0]]) {
      fields[fieldKey + '.' + err.path[0]] = {
        message:
          fields[fieldKey + '.' + err.path[0]].message + ', ' + err.message,
      }
    } else {
      fields[fieldKey + '.' + err.path[0]] = {
        message: err.message,
      }
    }
  })
  return {
    fields,
    message: error.message,
    stack: error.stack,
    name: error.name,
    status: 422,
  }
}

export class ZodErrorToValidateError<T> extends ValidateError {
  constructor(error: ZodError<T>, fieldKey: string = 'body') {
    const validError = ZodErrorToValidateError_<T>(error, fieldKey)
    super(validError.fields, validError.message)
  }
}
