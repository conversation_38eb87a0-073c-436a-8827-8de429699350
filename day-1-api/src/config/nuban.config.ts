import { EnvVars } from '../constants/env'
import { logger } from '../helpers/logger'
export class NUBANService {
  private baseURL: string = `https://app.nuban.com.ng/api/${EnvVars.nubanKey}`
  private predictionBaseURL: string = `https://app.nuban.com.ng/possible-banks/${EnvVars.nubanKey}`
  constructor() {}
  public async verifyAccountNumber(body: {
    accountNumber: string
    bankCode: string
  }) {
    try {
      const response = await fetch(
        `${this.baseURL}?bank_code=${body.bankCode}&acc_no=${body.accountNumber}`,
      )

      if (!response.ok) {
        logger(module).error(JSON.stringify(response, null, 2))
        return {
          data: null,
          error: new Error(`${response.status} Invalid account number or bank`),
        }
      }

      const data = await response.json()

      if (data.error) {
        logger(module).error(JSON.stringify(response, null, 2))
        return {
          data: null,
          error: new Error(data.message),
        }
      }
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
  public async predictAccountNumber(body: { accountNumber: string }) {
    try {
      const response = await fetch(
        `${this.predictionBaseURL}?acc_no=${body.accountNumber}`,
      )

      if (!response.ok) {
        logger(module).error(JSON.stringify(response, null, 2))

        return {
          data: null,
          error: new Error('Account number does not match known patterns'),
        }
      }

      const data = await response.json()
      if (data.error) {
        logger(module).error(JSON.stringify(response, null, 2))

        return {
          data: null,
          error: new Error(data.message),
        }
      }
      return { data, error: null }
    } catch (error) {
      logger(module).error(JSON.stringify(error, null, 2))
      return { data: null, error }
    }
  }
}

export const bankCodeWeights: number[] = [3, 7, 3, 3, 7, 3]

export const serialNumberWeights: number[] = [3, 7, 3, 3, 7, 3, 3, 7, 3]

export const calculateWeightedSum = (
  value: string,
  weights: number[],
): number => {
  if (value.length !== weights.length) {
    throw new Error('value and weights must have the same length')
  }

  return value
    .split('')
    .reduce((sum, digit, index) => sum + Number(digit) * weights[index], 0)
}

export const computeCheckDigit = (bankCode: string, serialNumber: string) => {
  const result =
    calculateWeightedSum(bankCode, bankCodeWeights) +
    calculateWeightedSum(serialNumber, serialNumberWeights)

  const subtractionResult = 10 - (result % 10)

  return subtractionResult === 10 ? 0 : subtractionResult
}

export const isBankAccountValid = (accountNumber: string, bankCode: string) => {
  if (accountNumber.length !== 10) {
    throw new Error(
      'Invalid account number, account number must be 10 digits long',
    )
  }

  let paddedBankCode = bankCode.replace(/\D/g, '')

  if (paddedBankCode.length === 3) {
    paddedBankCode = `000${paddedBankCode}`
  } else if (paddedBankCode.length === 5) {
    paddedBankCode = `9${paddedBankCode}`
  }

  if (paddedBankCode.length !== 6) {
    throw new Error(
      `Invalid bank code, bank code must be 3, 5 or 6 digits long. ${paddedBankCode} is ${paddedBankCode.length} digits long`,
    )
  }

  const serialNumber = accountNumber.substring(0, 9)

  const accountCheckDigit = accountNumber[9]

  const checkDigit = computeCheckDigit(paddedBankCode, serialNumber)

  return checkDigit?.toString() === accountCheckDigit
}

export const getPossibleBanks = (
  accountNumber: string,
  banks: any[],
): any[] => {
  return banks.filter((bank) => isBankAccountValid(accountNumber, bank.code))
}
