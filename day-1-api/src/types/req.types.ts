import { Request } from 'express'
export type UserPayload = {
  id: string
  email: string
  phoneNumber: string | null
  updatedAt: Date
  createdAt: Date
}
export interface AuthenticatedRequest extends Request {
  user?: UserPayload
}
export type User = {
  email: string
  id: string
  createdAt: Date
  updatedAt: Date
  phoneNumber: string | null
  phoneVerifiedAt: Date | null
  emailVerifiedAt: Date | null
  passwordSetAt: Date | null
  biometricsSetAt: Date | null
  pinSetAt: Date | null
  stripeCustomerId: string | null
}

export type transactionStatus = 'pending' | 'success' | 'failed'
export type transactionType = 'credit' | 'withdrawal' | 'transfer'

export type XAuthChallenge = {
  timestamp: number
  eventType: string
}

export type BiometricsKeyPayload = {
  email: string | null
  phoneNumber: string | null
}
export type PassportKycAttemptResponse = {
  id: number
  userId: string
  sessionId: string
  firstName: string
  lastName: string
  dateOfBirth: Date
  documentNumber: string
  documentType: string
  reason: string | null
  expiryDate: Date
  country: string
  createdAt: Date
  updatedAt: Date
  status: string | null
}
export type VisaKycAttemptResponse = {
  id: number
  createdAt: Date
  updatedAt: Date
  firstName: string
  lastName: string
  dateOfBirth: Date
  status: string | null
  country: string
  visaNumber: string
  visaType: string
  visaIssueDate: Date
  visaExpiryDate: Date
  userId: string
  sessionId: string
}

export type NubanBank = {
  bank_name: string
  code: string
}
export type PaystackBank = {
  id: number
  name: string
  slug: string
  code: string
  longcode: string
  gateway: string
  pay_with_bank: boolean
  supports_transfer: boolean
  active: boolean
  country: string
  currency: string
  type: 'nuban'
  is_deleted: boolean
  createdAt: string
  updatedAt: string
}

export type PaystackTransferEvent = {
  event: 'transfer.reversed' | 'transfer.failed' | 'transfer.success'
  data: {
    amount: number
    currency: string
    domain: string
    failures: null
    id: number
    integration: {
      id: number
      is_live: boolean
      business_name: string
    }
    reason: string
    reference: string
    source: string
    source_details: null
    status: string
    titan_code: null
    transfer_code: string
    transferred_at: string
    recipient: {
      active: boolean
      currency: string
      description: null
      domain: string
      email: string
      id: number
      integration: number
      metadata: null
      name: string
      recipient_code: string
      type: 'nuban'
      is_deleted: boolean
      details: {
        authorization_code: null
        account_number: string
        account_name: string
        bank_code: string
        bank_name: string
      }
      created_at: string
      updated_at: string
    }

    session: {
      provider: string
      id: string
    }
    created_at: string
    updated_at: string
  }
}

export type PaginationData<
  T extends Record<string, unknown> | ArrayLike<unknown> | undefined,
> = {
  paginationDetails: {
    totalPages: number
    totalItems: number
    currentPage: number
    hitsPerPage: number
  }
  data: T
}

export type UserBeneficiaries = {
  BeneficiaryDetails: {
    id: number
    createdAt: Date
    bankName: string
    bankCode: string
    paystackRecipientId: string
    accountName: string
    accountNumber: string
  }
  id: number
  createdAt: Date
  userId: string
  beneficiaryDetailId: number
  lastUsedAt: Date
  NoOfTransactions: number
  isFavorite: boolean
}
