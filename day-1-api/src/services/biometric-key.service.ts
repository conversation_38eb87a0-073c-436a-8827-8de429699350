import {
  randomBytes,
  createC<PERSON><PERSON><PERSON>,
  createDecipher<PERSON>,
  scryptSync,
  CipherGCM,
  DecipherGCM,
} from 'crypto'

/**
 * Configuration for the token system
 */
interface TokenConfig {
  encryptionKey: string
  algorithm?: string
}

export class BiometricKeyService {
  private readonly algorithm: string
  private readonly key: Buffer

  constructor(config: TokenConfig) {
    this.algorithm = config.algorithm || 'aes-256-gcm'
    // Generate a secure key from the provided encryption key
    this.key = scryptSync(config.encryptionKey, 'salt', 32)
  }

  /**
   * Generates a secure token with encoded data
   * @param data The data to encode in the token
   * @returns A secure token containing the encoded data
   */
  generateToken<T extends Record<string, unknown>>(data: T): string {
    // Generate a random IV
    const iv = randomBytes(12)
    const cipher = createCipheriv(this.algorithm, this.key, iv) as CipherGCM

    // Encrypt the data
    const jsonData = JSON.stringify(data)
    const encryptedData = Buffer.concat([
      cipher.update(jsonData, 'utf8'),
      cipher.final(),
    ])

    // Get the auth tag
    const authTag = cipher.getAuthTag()

    // Combine all components
    const tokenComponents = Buffer.concat([iv, authTag, encryptedData])

    // Return as base64url string
    return tokenComponents.toString('base64url')
  }

  /**
   * Decodes a token
   * @param token The token to decode
   * @returns The decoded data or null if invalid
   */
  decodeToken<T extends Record<string, unknown>>(token: string): T | null {
    try {
      // Convert token back to buffer
      const tokenBuffer = Buffer.from(token, 'base64url')

      // Extract components
      const iv = tokenBuffer.slice(0, 12)
      const authTag = tokenBuffer.slice(12, 28)
      const encryptedData = tokenBuffer.slice(28)

      // Create decipher
      const decipher = createDecipheriv(
        this.algorithm,
        this.key,
        iv,
      ) as DecipherGCM
      decipher.setAuthTag(authTag)

      // Decrypt the data
      const decrypted = Buffer.concat([
        decipher.update(encryptedData),
        decipher.final(),
      ])

      return JSON.parse(decrypted.toString())
    } catch {
      return null
    }
  }
}
