import { RSAEncryptDecryptService } from './rsa-encrypt-decrypt.service'

export class EncryptionHandler {
  private encryptionHandler: RSAEncryptDecryptService
  constructor(private readonly keyIdentifier: string) {
    this.encryptionHandler = new RSAEncryptDecryptService()
    this.keyIdentifier = keyIdentifier
  }

  async generateAuthKeys() {
    const hasGeneratedAuthKeys = await this.checkAuthKeyPresence()
    if (!hasGeneratedAuthKeys) {
      this.encryptionHandler.saveKeys(this.keyIdentifier)
    }
  }
  async checkAuthKeyPresence() {
    try {
      const keyPairs = await this.encryptionHandler.listKeyPairs()
      return keyPairs.includes(this.keyIdentifier)
    } catch {
      return false
    }
  }
  decryptData<T extends Record<string, unknown>>(encryptedData: string) {
    return this.encryptionHandler.decryptData<T>(
      encryptedData,
      this.keyIdentifier,
    )
  }
  encryptData<T extends Record<string, unknown>>(data: T) {
    return this.encryptionHandler.encryptData(data, this.keyIdentifier)
  }
  getPublicKey() {
    return this.encryptionHandler.readPublicKeyFromFileSystem(
      this.keyIdentifier,
    )
  }
  getPrivateKey() {
    return this.encryptionHandler.readPrivateKeyFromFileSystem(
      this.keyIdentifier,
    )
  }
}

export class AuthEncryptionHandler extends EncryptionHandler {
  constructor() {
    super('x-auth-challenge')
  }
}
