/*
  Warnings:

  - Added the required column `exchangeRate` to the `WalletBalance` table without a default value. This is not possible if the table is not empty.
  - Added the required column `transactionStatus` to the `WalletBalance` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "TransactionStatus" AS ENUM ('pending', 'success', 'failed');

-- AlterTable
ALTER TABLE "UserWallet" ALTER COLUMN "balance" SET DATA TYPE DECIMAL(20,2),
ALTER COLUMN "baseBalance" SET DATA TYPE DECIMAL(20,2);

-- AlterTable
ALTER TABLE "WalletBalance" ADD COLUMN     "beneficiaryId" INTEGER,
ADD COLUMN     "exchangeRate" JSONB NOT NULL,
ADD COLUMN     "transactionStatus" "TransactionStatus" NOT NULL;

-- AddForeignKey
ALTER TABLE "WalletBalance" ADD CONSTRAINT "WalletBalance_beneficiaryId_fkey" FOREIGN KEY ("beneficiaryId") REFERENCES "UserBeneficiaries"("id") ON DELETE CASCADE ON UPDATE CASCADE;
