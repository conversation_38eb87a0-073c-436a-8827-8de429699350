/*
  Warnings:

  - You are about to drop the column `statusName` on the `TransactionType` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[typeName]` on the table `TransactionType` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `typeName` to the `TransactionType` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "TransactionType_statusName_key";

-- AlterTable
ALTER TABLE "TransactionType" DROP COLUMN "statusName",
ADD COLUMN     "typeName" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "TransactionType_typeName_key" ON "TransactionType"("typeName");
