-- CreateTable
CREATE TABLE "VisaDetails" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "visaNumber" VARCHAR(256) NOT NULL,
    "visaType" VARCHAR(256) NOT NULL,
    "visaIssueDate" TIMESTAMP(3) NOT NULL,
    "visaExpiryDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "VisaDetails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserKycAttempts" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "firstName" VARCHAR(256) NOT NULL,
    "lastName" VARCHAR(256) NOT NULL,
    "dateOfBirth" TIMESTAMP(3) NOT NULL,
    "documentNumber" VARCHAR(256) NOT NULL,
    "documentType" VARCHAR(256) NOT NULL,
    "reason" VARCHAR(256),
    "country" VARCHAR(2) NOT NULL,
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,
    "status" VARCHAR(256),

    CONSTRAINT "UserKycAttempts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserKycDetails" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "attemptId" INTEGER NOT NULL,
    "gender" VARCHAR(1),
    "idNumber" VARCHAR(256),
    "lastName" VARCHAR(256),
    "firstName" VARCHAR(256),
    "nationality" VARCHAR(256),
    "dateOfBirth" TIMESTAMP(3),
    "yearOfBirth" TEXT,
    "placeOfBirth" VARCHAR(256),
    "occupation" VARCHAR(256),
    "extraNames" VARCHAR(256),
    "employer" VARCHAR(256),
    "riskScore" DECIMAL(3,2),
    "riskLabels" JSONB,
    "additionalVerifiedData" JSONB,
    "document" JSONB,
    "ip" VARCHAR(16),
    "decisionTime" TIMESTAMPTZ(3),
    "acceptanceTime" TIMESTAMPTZ(3),
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "UserKycDetails_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "VisaDetails_userId_key" ON "VisaDetails"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserKycDetails_userId_key" ON "UserKycDetails"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserKycDetails_attemptId_key" ON "UserKycDetails"("attemptId");

-- AddForeignKey
ALTER TABLE "VisaDetails" ADD CONSTRAINT "VisaDetails_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserKycAttempts" ADD CONSTRAINT "UserKycAttempts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserKycDetails" ADD CONSTRAINT "UserKycDetails_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserKycDetails" ADD CONSTRAINT "UserKycDetails_attemptId_fkey" FOREIGN KEY ("attemptId") REFERENCES "UserKycAttempts"("id") ON DELETE CASCADE ON UPDATE CASCADE;
