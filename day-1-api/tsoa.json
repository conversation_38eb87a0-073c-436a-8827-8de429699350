{"entryFile": "src/app.ts", "noImplicitAdditionalProperties": "throw-on-extras", "controllerPathGlobs": ["src/**/*.controller.ts"], "spec": {"outputDirectory": "build", "specVersion": 3, "securityDefinitions": {"jwt": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "tags": [{"name": "Authentication Controller"}, {"name": "Payments and Wallets Controller"}, {"name": "User Profile Controller"}]}, "routes": {"routesDir": "build", "iocModule": "src/lib/tsyringeTsoaIocContainer", "authenticationModule": "./src/middleware/auth.middleware.ts"}}