#!/bin/bash

# Deploy to Cloud Run with Cloud SQL Proxy (Alternative approach)
# This approach uses Cloud SQL Proxy for more reliable connections

PROJECT_ID="day-one-465214"  # Replace with your actual project ID
REGION="us-central1"
SERVICE_NAME="day-one-api"
CLOUD_SQL_INSTANCE="day-one-db"  # Replace with your actual Cloud SQL instance name
CLOUD_SQL_CONNECTION_NAME="${PROJECT_ID}:${REGION}:${CLOUD_SQL_INSTANCE}"

# Build and deploy using Cloud Build
echo "Building and deploying to Cloud Run..."

gcloud run deploy $SERVICE_NAME \
  --source . \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 3000 \
  --add-cloudsql-instances $CLOUD_SQL_CONNECTION_NAME \
  --set-env-vars NODE_ENV=production \
  --set-env-vars PORT=3000 \
  --set-env-vars DATABASE_URL="postgresql://day_one_user:day1api@127.0.0.1:5432/day_one_db" \
  --set-env-vars DIRECT_URL="postgresql://day_one_user:day1api@127.0.0.1:5432/day_one_db" \
  --set-env-vars SALT_ROUNDS_FOR_HASH=12 \
  --set-env-vars JWT_SECRET_KEY="Eu_fmTQvkuHddkmMOCCcRjHGiDGywD7aj7Beh3oB_i4rJ5VkglflBh8nZVoxkF60YmHHdFPPyAyNWsOEAruSzkAKoVYRVdBTMgAAF9RmdySvbpmGJmCt-jM79YvwjVb2" \
  --set-env-vars RT_SECRET_KEY="9KrCpikWmBvsHMy2D8X_9G5lf6KT_khLhvTwDrHtECPp7c_Mx6lJqPhJuL4XL2b3CeKUn7J_JdThwPz_-VS-gZJISQiQhRyd0RorqZvgTJhPdidtlkVGXi4nM4xYe6c3" \
  --set-env-vars BIO_SECRET_KEY="uJrfh_X4d1b5GeGAaQCDhOqm3nP5lvsgrUeKlJE5M6UDip0cKBsHhaaAGyyq8xQFcK4JL4Msq2THpzDXT_3XTm18FdhDSu6N8CQa4z25MQlepi5b7nTwENL8It6R3msC" \
  --set-env-vars JWT_EXPIRY="7d" \
  --set-env-vars RT_EXPIRY="7d" \
  --set-env-vars TWILIO_ACCOUNT_SID="**********************************" \
  --set-env-vars TWILIO_AUTH_TOKEN="1ba91e2d3ee8b7df790d86f94e68f6e9" \
  --set-env-vars TWILIO_VERIFY_SERVICE_SID="VA5cbc7887583ddaa09dfba1ab5dd1e818" \
  --set-env-vars RESEND_OUTGOING_KEY="re_dSuxgrrQ_FYasa2kbjZvvqyLuVUgHn7uK" \
  --set-env-vars STRIPE_PUBLISHABLE_KEY="pk_test_51NMSiEGJgPsZZ1nzQZFWNT7UrKI2hv3OC12Npy7s6fT6wT5MivY8UFTZ97YY3hCIn8j2dIPwrHn4jiaRjIL7DEfU00Rk52Fwmj" \
  --set-env-vars STRIPE_SECRET_KEY="sk_test_51NMSiEGJgPsZZ1nzwHiNcV3SHFGtRiAcw5eK5IvR1nBAji4PAIS7QlveSMr78eFUIPt7EP5JcxD2TMrvyn6aXno300M28KfOoP" \
  --set-env-vars STRIPE_WEBHOOK_SECRET="whsec_w2oPotf6OtAxNTbf4koSn4onnaVnbeaL" \
  --set-env-vars NUBAN_API_KEY="NUBAN-FQJYDQTE2231" \
  --set-env-vars VERIF_MASTER_SIGNATURE_KEY="97853b21-2742-4366-a947-5bce9c71df92" \
  --set-env-vars VERIF_API_KEY="b353b0f3-b763-41bf-9d49-1ab9b067e78d" \
  --set-env-vars PAYSTACK_SECRET_KEY="sk_test_608430188e5c5442679570f9f46d3176097dc9b5" \
  --cpu=1 \
  --memory=1Gi \
  --timeout=300 \
  --concurrency=100 \
  --max-instances=10

echo "Deployment completed!"
echo "Service URL:"
gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)'